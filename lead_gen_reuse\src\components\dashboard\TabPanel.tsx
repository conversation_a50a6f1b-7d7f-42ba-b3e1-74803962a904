import React from 'react';
import type { Business } from '../../types/business';
import type { SearchParams } from '../../types/common';
import { BusinessTable } from '../business/BusinessTable';
import { DataSource } from '../../types/common';

interface TabPanelProps {
  dataSource: DataSource;
  businesses: Business[];
  searchParams: SearchParams;
}

const DATA_SOURCE_INFO: Record<DataSource, {
  description: string;
  features: string[];
  icon: string;
}> = {
  [DataSource.GOOGLE_PLACES]: {
    description: 'High-quality business data with ratings, reviews, and detailed information from Google Places API.',
    features: ['Ratings & Reviews', 'Photos', 'Opening Hours', 'Contact Info', 'Categories'],
    icon: '🏢'
  },
  [DataSource.OVERPASS]: {
    description: 'Comprehensive business data from OpenStreetMap with geographic information and local coverage.',
    features: ['Geographic Data', 'Local Coverage', 'Address Details', 'Business Types', 'Location Data'],
    icon: '🗺️'
  },
  [DataSource.YELLOW_PAGES]: {
    description: 'AI-powered web scraping of Yellow Pages for additional business details and contact information.',
    features: ['Contact Details', 'Business Descriptions', 'Additional Info', 'Web Presence', 'AI Extraction'],
    icon: '📞'
  },
  [DataSource.MANUAL]: {
    description: 'Manually entered business data.',
    features: ['Custom Data', 'Manual Entry', 'User Input'],
    icon: '✏️'
  },
  [DataSource.TEST]: {
    description: 'Test data for development purposes.',
    features: ['Test Data', 'Development', 'Mock Data'],
    icon: '🧪'
  }
};

export const TabPanel: React.FC<TabPanelProps> = ({
  dataSource,
  businesses,
  searchParams
}) => {
  const info = DATA_SOURCE_INFO[dataSource];

  if (businesses.length === 0) {
    return (
      <div className="p-16 text-center">
        <div className="relative mb-8">
          <div className="text-8xl mb-4 animate-bounce">{info.icon}</div>
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full blur-3xl"></div>
        </div>
        <h3 className="text-2xl font-bold text-white mb-4">
          No businesses found
        </h3>
        <p className="text-gray-400 mb-8 max-w-lg mx-auto text-lg">
          {searchParams.query
            ? `No results found for "${searchParams.query}" in ${searchParams.location || 'the specified area'}.`
            : 'Enter a search query above to discover businesses and generate leads.'
          }
        </p>

        {/* Modern Data Source Info Card */}
        <div className="bg-gradient-to-br from-gray-700 to-gray-600 rounded-2xl p-8 max-w-3xl mx-auto border border-gray-500 shadow-2xl">
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg mr-4">
              <span className="text-2xl">{info.icon}</span>
            </div>
            <div>
              <h4 className="text-xl font-bold text-white mb-2">
                About {DATA_SOURCE_INFO[dataSource].description.split(' ')[0]} {DATA_SOURCE_INFO[dataSource].description.split(' ')[1]}
              </h4>
              <p className="text-gray-300">
                {info.description}
              </p>
            </div>
          </div>
          <div className="flex flex-wrap gap-3 justify-center">
            {info.features.map((feature) => (
              <span
                key={feature}
                className="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg transform hover:scale-105 transition-transform"
              >
                {feature}
              </span>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Results Summary */}
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">{info.icon}</span>
          <div>
            <h3 className="text-lg font-medium text-white">
              {businesses.length} businesses found
            </h3>
            <p className="text-sm text-gray-400">
              {searchParams.query && `Results for "${searchParams.query}"`}
              {searchParams.location && ` in ${searchParams.location}`}
            </p>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="flex space-x-4 text-sm text-gray-400">
          <div className="text-center">
            <div className="font-medium text-white">
              {businesses.filter(b => b.rating?.rating && b.rating.rating >= 4).length}
            </div>
            <div>High Rated</div>
          </div>
          <div className="text-center">
            <div className="font-medium text-white">
              {businesses.filter(b => b.contact?.phone).length}
            </div>
            <div>With Phone</div>
          </div>
          <div className="text-center">
            <div className="font-medium text-white">
              {businesses.filter(b => b.contact?.website).length}
            </div>
            <div>With Website</div>
          </div>
        </div>
      </div>

      {/* Business Table */}
      <BusinessTable 
        businesses={businesses}
        dataSource={dataSource}
      />
    </div>
  );
};
