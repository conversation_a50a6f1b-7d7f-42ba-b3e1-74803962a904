from pydantic import BaseModel, Field, validator
from typing import Optional
import re

class BusinessData(BaseModel):
    name: str = Field(..., description="The name of the business or entity (required).")
    address: Optional[str] = Field(None, description="The address of the business if explicitly shown.")
    phone_number: Optional[str] = Field(None, description="The phone number if explicitly displayed.")
    website: Optional[str] = Field(None, description="The website URL if explicitly shown.")
    description: Optional[str] = Field(None, description="A brief description based on visible content.")

    @validator('phone_number')
    def validate_phone_number(cls, v):
        if v is None or v == "":
            return None
        # Basic validation - must contain at least some digits
        if not re.search(r'\d{3}', v):  # At least 3 digits
            return None
        # Filter out obviously fake numbers
        if '555-555-' in v or v.count('0') > 7:
            return None
        return v

    @validator('website')
    def validate_website(cls, v):
        if v is None or v == "":
            return None
        # Basic URL validation
        if not re.match(r'^https?://', v) and not v.startswith('www.'):
            return None  # Invalid URL format, return None
        return v

    @validator('description')
    def validate_description(cls, v):
        if v is None or v == "":
            return None
        # Only reject obviously fake descriptions
        if len(v) < 10:  # Too short to be meaningful
            return None
        return v