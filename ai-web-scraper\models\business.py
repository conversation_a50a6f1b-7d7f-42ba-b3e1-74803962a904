from pydantic import BaseModel, Field, validator
from typing import Optional
import re

class BusinessData(BaseModel):
    name: str = Field(..., description="The name of the business or entity (required).")
    address: Optional[str] = Field(None, description="The address of the business if explicitly shown.")
    phone_number: Optional[str] = Field(None, description="The phone number if explicitly displayed.")
    website: Optional[str] = Field(None, description="The website URL if explicitly shown.")
    description: Optional[str] = Field(None, description="A brief description based on visible content.")

    @validator('phone_number')
    def validate_phone_number(cls, v):
        if v is None or v == "":
            return None
        # Basic phone number validation - must contain digits and common phone patterns
        if not re.search(r'\d{3}[-.\s]?\d{3}[-.\s]?\d{4}|\(\d{3}\)\s?\d{3}[-.\s]?\d{4}', v):
            return None  # Invalid phone format, return None instead of made-up number
        return v

    @validator('website')
    def validate_website(cls, v):
        if v is None or v == "":
            return None
        # Basic URL validation
        if not re.match(r'^https?://', v) and not v.startswith('www.'):
            return None  # Invalid URL format, return None
        return v

    @validator('description')
    def validate_description(cls, v):
        if v is None or v == "":
            return None
        # Reject generic/templated descriptions that might be made up
        generic_phrases = [
            "offers a range of", "provides services", "specializes in",
            "committed to providing", "dedicated to", "serving the community"
        ]
        if any(phrase in v.lower() for phrase in generic_phrases):
            return None  # Likely generated description
        return v