import React from 'react';
import { RouterProvider, useRouter } from './components/router/Router';
import { Navigation } from './components/navigation/Navigation';
import { GooglePlacesPage } from './components/pages/GooglePlacesPage';
import { OverpassPage } from './components/pages/OverpassPage';
import { YellowPagesPage } from './components/pages/YellowPagesPage';
import './App.css';

const AppContent: React.FC = () => {
  const { currentRoute } = useRouter();

  const renderCurrentPage = () => {
    switch (currentRoute) {
      case 'google-places':
        return <GooglePlacesPage />;
      case 'overpass':
        return <OverpassPage />;
      case 'yellow-pages':
        return <YellowPagesPage />;
      default:
        return <GooglePlacesPage />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-900 to-gray-800">
      {/* Modern Header with Gradient */}
      <header className="bg-gradient-to-r from-gray-800 to-gray-700 shadow-2xl border-b border-gray-600 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-8">
            <div className="flex items-center space-x-4">
              {/* Modern Logo/Icon */}
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div>
                <h1 className="text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                  Lead Generation Dashboard
                </h1>
                <p className="mt-2 text-sm text-gray-400 font-medium">
                  Multi-source business data collection platform
                </p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Navigation />
        {renderCurrentPage()}
      </main>
    </div>
  );
};

function App() {
  return (
    <RouterProvider initialRoute="google-places">
      <AppContent />
    </RouterProvider>
  );
}

export default App;
