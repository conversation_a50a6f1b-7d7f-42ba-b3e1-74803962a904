# Yellow Pages Scraper API Setup

This directory contains the fresh Yellow Pages scraper from the kaymen99/ai-web-scraper repository, configured to work as a backend API for the lead generation dashboard.

## Quick Start

1. **Install Dependencies**
   ```bash
   cd ai-web-scraper
   pip install -r requirements.txt
   playwright install
   ```

2. **Set up Environment Variables**
   ```bash
   cp .env.example .env
   # Edit .env and add your API keys
   ```

3. **Start the API Server**
   ```bash
   python api_server.py
   # Or use the startup scripts:
   # Windows: start_api_server.bat
   # Linux/Mac: ./start_api_server.sh
   ```

4. **Test the API**
   The server will run on http://localhost:8003
   - API docs: http://localhost:8003/docs
   - Health check: http://localhost:8003/health

## API Endpoints

### POST /scrape
Scrape businesses from Yellow Pages

**Request Body:**
```json
{
  "category": "dentists",
  "location": "Toronto ON",
  "max_pages": 3,
  "region": "ca"
}
```

**Response:**
```json
{
  "businesses": [
    {
      "name": "Business Name",
      "address": "123 Main St",
      "phone": "(*************",
      "website": "https://example.com",
      "description": "Business description",
      "categories": ["dentist"],
      "scraped_at": "2025-01-16T20:00:00Z"
    }
  ],
  "total_found": 1,
  "page": 1,
  "per_page": 1,
  "search_query": "dentists",
  "location": "Toronto ON",
  "has_more_pages": false,
  "scraped_at": "2025-01-16T20:00:00Z"
}
```

## Configuration

Edit `config.py` to customize:
- `LLM_MODEL`: AI model to use (default: "gemini/gemini-2.0-flash")
- `MAX_PAGES`: Maximum pages to scrape (default: 3)

## Supported LLM Providers

- **OpenAI**: Set `OPENAI_API_KEY` and use models like "gpt-4o"
- **Google Gemini**: Set `GEMINI_API_KEY` and use "gemini/gemini-2.0-flash"
- **Anthropic Claude**: Set `ANTHROPIC_API_KEY` and use "claude-3-sonnet"
- **Groq**: Set `GROQ_API_KEY` and use "groq/llama-3.1-8b-instant"

## Troubleshooting

1. **Import Errors**: Make sure all dependencies are installed
2. **API Key Errors**: Check your .env file has the correct API key for your chosen model
3. **Playwright Issues**: Run `playwright install` to install browser dependencies
4. **Port Conflicts**: The server runs on port 8003 by default

## Integration with Dashboard

The TypeScript dashboard in `lead_gen_reuse` is configured to connect to this API server automatically. Just start this server and the Yellow Pages tab in the dashboard will work.
