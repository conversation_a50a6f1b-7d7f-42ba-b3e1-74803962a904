/**
 * Overpass API (OpenStreetMap) search page with amenity type dropdowns
 */

import React, { useState } from 'react';
import { OVERPASS_AMENITY_TYPES, type OverpassAmenityType } from '../../constants/business-types';
import { OverpassClient, overpassTransformer } from '../../apis/overpass';

interface OverpassSearchParams {
  amenityTypes: OverpassAmenityType[];
  location: string;
  radius: number;
  limit: number;
}

export const OverpassPage: React.FC = () => {
  const [searchParams, setSearchParams] = useState<OverpassSearchParams>({
    amenityTypes: [],
    location: '',
    radius: 5000,
    limit: 50
  });
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Group amenity types by category using comprehensive list
  const amenityGroups = {
    'Sustenance': ['bar', 'biergarten', 'cafe', 'fast_food', 'food_court', 'ice_cream', 'pub', 'restaurant'],
    'Education': ['college', 'dancing_school', 'driving_school', 'first_aid_school', 'kindergarten', 'language_school', 'library', 'music_school', 'research_institute', 'school', 'surf_school', 'toy_library', 'traffic_park', 'training', 'university'],
    'Transportation': ['bicycle_parking', 'bicycle_repair_station', 'bicycle_rental', 'bicycle_wash', 'boat_rental', 'boat_sharing', 'bus_station', 'car_rental', 'car_sharing', 'car_wash', 'charging_station', 'compressed_air', 'driver_training', 'ferry_terminal', 'fuel', 'grit_bin', 'motorcycle_parking', 'parking', 'parking_entrance', 'parking_space', 'taxi', 'vehicle_inspection', 'weighbridge'],
    'Financial': ['atm', 'bank', 'bureau_de_change', 'money_transfer', 'payment_centre', 'payment_terminal'],
    'Healthcare': ['baby_hatch', 'clinic', 'dentist', 'doctors', 'hospital', 'nursing_home', 'pharmacy', 'social_facility', 'veterinary'],
    'Entertainment & Culture': ['arts_centre', 'brothel', 'casino', 'cinema', 'community_centre', 'conference_centre', 'events_venue', 'exhibition_centre', 'fountain', 'gambling', 'love_hotel', 'music_venue', 'nightclub', 'planetarium', 'public_bookcase', 'social_centre', 'stage', 'stripclub', 'studio', 'swingerclub', 'theatre'],
    'Public Service': ['courthouse', 'fire_station', 'police', 'post_box', 'post_depot', 'post_office', 'prison', 'ranger_station', 'townhall'],
    'Facilities': ['bbq', 'bench', 'dog_toilet', 'dressing_room', 'drinking_water', 'give_box', 'lounge', 'mailroom', 'parcel_locker', 'shelter', 'shower', 'telephone', 'toilets', 'water_point', 'watering_place'],
    'Waste Management': ['recycling', 'sanitary_dump_station', 'waste_basket', 'waste_disposal', 'waste_transfer_station'],
    'Other': ['animal_boarding', 'animal_breeding', 'animal_shelter', 'animal_training', 'baking_oven', 'clock', 'crematorium', 'dive_centre', 'funeral_hall', 'grave_yard', 'hunting_stand', 'internet_cafe', 'kitchen', 'kneipp_water_cure', 'lounger', 'marketplace', 'monastery', 'mortuary', 'photo_booth', 'place_of_mourning', 'place_of_worship', 'public_bath', 'public_building', 'refugee_site', 'vending_machine']
  };

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (searchParams.amenityTypes.length === 0 || !searchParams.location) return;

    setLoading(true);
    setError(null);
    setResults([]);

    try {
      const client = new OverpassClient({
        timeout: 30000,
        requestsPerSecond: 1, // Conservative rate limit
      });

      // Simple geocoding for location (you could also use Google's geocoding API here)
      const geocodeResponse = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(searchParams.location)}&limit=1`
      );

      if (!geocodeResponse.ok) {
        throw new Error('Failed to geocode location. Please try a different location.');
      }

      const geocodeData = await geocodeResponse.json();
      if (!geocodeData || geocodeData.length === 0) {
        throw new Error('Location not found. Please try a different location.');
      }

      const location = {
        latitude: parseFloat(geocodeData[0].lat),
        longitude: parseFloat(geocodeData[0].lon)
      };

      const allResults: any[] = [];

      // Search for each amenity type
      for (const amenityType of searchParams.amenityTypes) {
        console.log(`Searching for ${amenityType} near ${searchParams.location}...`);

        try {
          const searchResponse = await client.searchBusinesses(
            amenityType,
            location,
            searchParams.radius,
            {
              limit: Math.min(100, Math.floor(searchParams.limit / searchParams.amenityTypes.length)),
              includeDetails: true
            }
          );

          if (searchResponse.elements && searchResponse.elements.length > 0) {
            // Transform the results
            const transformedResults = await Promise.all(
              searchResponse.elements.map(element => overpassTransformer(element))
            );
            allResults.push(...transformedResults);
          }

          // Add a delay between requests to respect rate limits
          if (searchParams.amenityTypes.length > 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        } catch (typeError) {
          console.warn(`Failed to search for ${amenityType}:`, typeError);
          // Continue with other types even if one fails
        }
      }

      // Remove duplicates and limit results
      const uniqueResults = allResults
        .filter((result, index, self) =>
          index === self.findIndex(r => r.id === result.id)
        )
        .slice(0, searchParams.limit);

      setResults(uniqueResults);
      console.log(`Found ${uniqueResults.length} unique businesses from OpenStreetMap`);

    } catch (error) {
      console.error('Search failed:', error);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleAmenityTypeChange = (value: string) => {
    const amenityType = value as OverpassAmenityType;
    setSearchParams(prev => ({
      ...prev,
      amenityTypes: prev.amenityTypes.includes(amenityType)
        ? prev.amenityTypes.filter(type => type !== amenityType)
        : [...prev.amenityTypes, amenityType]
    }));
  };

  const handleReset = () => {
    setSearchParams({
      amenityTypes: [],
      location: '',
      radius: 5000,
      limit: 50
    });
    setResults([]);
    setError(null);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-white mb-4">
          🌍 OpenStreetMap (Overpass API)
        </h2>
        <p className="text-gray-400 max-w-2xl mx-auto">
          Query the world's largest open geographic database. Access community-contributed data 
          for businesses, amenities, and points of interest worldwide.
        </p>
      </div>

      {/* Search Form */}
      <div className="bg-gradient-to-br from-gray-800 to-gray-700 rounded-2xl p-8 border border-gray-600 shadow-2xl">
        <form onSubmit={handleSearch} className="space-y-6">
          {/* Amenity Type Selection */}
          <div className="space-y-2">
            <label className="block text-sm font-semibold text-gray-300 mb-2">
              🏢 Amenity Types (Select Multiple)
            </label>

            {/* Selected Types Display */}
            {searchParams.amenityTypes.length > 0 && (
              <div className="mb-4 p-3 bg-gray-700/30 rounded-lg border border-gray-600">
                <div className="flex flex-wrap gap-2">
                  {searchParams.amenityTypes.map((type) => (
                    <span
                      key={type}
                      className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-500/20 text-green-300 border border-green-500/30"
                    >
                      {OVERPASS_AMENITY_TYPES[type]}
                      <button
                        type="button"
                        onClick={() => handleAmenityTypeChange(type)}
                        className="ml-2 text-green-400 hover:text-green-200 transition-colors"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
                <div className="mt-2 text-xs text-gray-400">
                  {searchParams.amenityTypes.length} type{searchParams.amenityTypes.length !== 1 ? 's' : ''} selected
                </div>
              </div>
            )}

            {/* Amenity Type Categories */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto bg-gray-700/20 rounded-xl p-4 border border-gray-600">
              {Object.entries(amenityGroups).map(([category, types]) => (
                <div key={category} className="space-y-2">
                  <h4 className="font-semibold text-green-300 text-sm border-b border-gray-600 pb-1">
                    {category}
                  </h4>
                  <div className="space-y-1">
                    {types.map((type) => (
                      <label
                        key={type}
                        className="flex items-center space-x-2 text-sm cursor-pointer hover:bg-gray-600/30 rounded p-1 transition-colors"
                      >
                        <input
                          type="checkbox"
                          checked={searchParams.amenityTypes.includes(type as OverpassAmenityType)}
                          onChange={() => handleAmenityTypeChange(type)}
                          className="rounded border-gray-500 text-green-500 focus:ring-green-500 focus:ring-2"
                        />
                        <span className="text-gray-300 hover:text-white transition-colors">
                          {OVERPASS_AMENITY_TYPES[type as OverpassAmenityType]}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Location and Parameters */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Location Input */}
            <div className="space-y-2">
              <label htmlFor="location" className="block text-sm font-semibold text-gray-300 mb-2">
                📍 Location
              </label>
              <div className="relative">
                <input
                  type="text"
                  id="location"
                  value={searchParams.location}
                  onChange={(e) => setSearchParams(prev => ({ ...prev, location: e.target.value }))}
                  placeholder="e.g., Berlin, Germany or coordinates"
                  className="block w-full rounded-xl bg-gray-700/50 border-2 border-gray-600 text-white placeholder-gray-400 shadow-lg focus:border-green-500 focus:ring-4 focus:ring-green-500/20 sm:text-sm transition-all duration-300 py-4 px-4 backdrop-blur-sm"
                  required
                />
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-green-500/10 to-blue-500/10 pointer-events-none"></div>
              </div>
            </div>

            {/* Radius */}
            <div className="space-y-2">
              <label htmlFor="radius" className="block text-sm font-semibold text-gray-300 mb-2">
                🎯 Search Radius
              </label>
              <div className="relative">
                <select
                  id="radius"
                  value={searchParams.radius}
                  onChange={(e) => setSearchParams(prev => ({ ...prev, radius: parseInt(e.target.value) }))}
                  className="block w-full rounded-xl bg-gray-700/50 border-2 border-gray-600 text-white shadow-lg focus:border-green-500 focus:ring-4 focus:ring-green-500/20 sm:text-sm transition-all duration-300 py-4 px-4 backdrop-blur-sm appearance-none cursor-pointer"
                >
                  <option value={1000}>1 km radius</option>
                  <option value={2000}>2 km radius</option>
                  <option value={5000}>5 km radius</option>
                  <option value={10000}>10 km radius</option>
                  <option value={25000}>25 km radius</option>
                  <option value={50000}>50 km radius</option>
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Max Results */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <label htmlFor="limit" className="block text-sm font-semibold text-gray-300 mb-2">
                📊 Max Results
              </label>
              <div className="relative">
                <select
                  id="limit"
                  value={searchParams.limit}
                  onChange={(e) => setSearchParams(prev => ({ ...prev, limit: parseInt(e.target.value) }))}
                  className="block w-full rounded-xl bg-gray-700/50 border-2 border-gray-600 text-white shadow-lg focus:border-green-500 focus:ring-4 focus:ring-green-500/20 sm:text-sm transition-all duration-300 py-4 px-4 backdrop-blur-sm appearance-none cursor-pointer"
                >
                  <option value={10}>10 results</option>
                  <option value={25}>25 results</option>
                  <option value={50}>50 results</option>
                  <option value={100}>100 results</option>
                  <option value={200}>200 results</option>
                  <option value={500}>500 results</option>
                  <option value={1000}>1000 results</option>
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-center space-x-4 pt-4">
            <button
              type="submit"
              disabled={loading || searchParams.amenityTypes.length === 0 || !searchParams.location}
              className="group relative inline-flex items-center px-8 py-4 border border-transparent text-sm font-bold rounded-xl shadow-lg text-white bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 focus:outline-none focus:ring-4 focus:ring-green-500/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
            >
              {loading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Searching...
                </>
              ) : (
                <>
                  <svg className="-ml-1 mr-3 h-5 w-5 group-hover:scale-110 transition-transform" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  Search OpenStreetMap
                </>
              )}
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-green-500 to-emerald-600 opacity-0 group-hover:opacity-20 transition-opacity"></div>
            </button>

            <button
              type="button"
              onClick={handleReset}
              disabled={loading}
              className="inline-flex items-center px-6 py-4 border-2 border-gray-600 text-sm font-semibold rounded-xl text-gray-300 bg-gray-700/50 hover:bg-gray-600/50 hover:border-gray-500 focus:outline-none focus:ring-4 focus:ring-gray-500/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 backdrop-blur-sm"
            >
              <svg className="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Reset
            </button>
          </div>
        </form>
      </div>

      {/* API Information */}
      <div className="bg-gradient-to-br from-green-900/20 to-emerald-900/20 rounded-2xl p-6 border border-green-500/30">
        <h3 className="text-lg font-semibold text-white mb-3">
          📋 OpenStreetMap Features
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="flex items-center space-x-2">
            <span className="text-green-400">✓</span>
            <span className="text-gray-300">Free and open data</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-green-400">✓</span>
            <span className="text-gray-300">Global coverage</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-green-400">✓</span>
            <span className="text-gray-300">Community-maintained</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-green-400">✓</span>
            <span className="text-gray-300">Detailed POI data</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-green-400">✓</span>
            <span className="text-gray-300">No API key required</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-green-400">✓</span>
            <span className="text-gray-300">Rich attribute data</span>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-900/20 border border-red-500/30 rounded-xl p-4">
          <div className="flex items-center space-x-2">
            <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-red-300 font-medium">Error</span>
          </div>
          <p className="text-red-200 mt-2">{error}</p>
        </div>
      )}

      {/* Results Display */}
      {results.length > 0 && (
        <div className="bg-gradient-to-br from-gray-800 to-gray-700 rounded-2xl p-8 border border-gray-600 shadow-2xl">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-2xl font-bold text-white">
              Search Results ({results.length})
            </h3>
            <button
              onClick={() => {
                const csv = convertToCSV(results);
                downloadCSV(csv, 'overpass-results.csv');
              }}
              className="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Export CSV
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
            {results.map((business, index) => (
              <div key={business.id || index} className="bg-gray-700/50 rounded-lg p-4 border border-gray-600">
                <h4 className="font-semibold text-white mb-2">{business.name || 'Unnamed Business'}</h4>
                <p className="text-gray-300 text-sm mb-2">{business.address}</p>
                {business.phone && (
                  <p className="text-green-300 text-sm mb-1">📞 {business.phone}</p>
                )}
                {business.website && (
                  <a
                    href={business.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-green-400 hover:text-green-300 text-sm underline"
                  >
                    🌐 Website
                  </a>
                )}
                {business.amenity && (
                  <p className="text-blue-300 text-sm mt-2">
                    🏢 {business.amenity}
                  </p>
                )}
                {business.openingHours && (
                  <p className="text-yellow-400 text-sm mt-1">
                    🕒 {business.openingHours}
                  </p>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// Helper functions for CSV export
const convertToCSV = (data: any[]) => {
  if (data.length === 0) return '';

  const headers = ['Name', 'Address', 'Phone', 'Website', 'Amenity', 'Opening Hours'];
  const rows = data.map(business => [
    business.name || '',
    business.address || '',
    business.phone || '',
    business.website || '',
    business.amenity || '',
    business.openingHours || ''
  ]);

  return [headers, ...rows].map(row =>
    row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(',')
  ).join('\n');
};

const downloadCSV = (csv: string, filename: string) => {
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
