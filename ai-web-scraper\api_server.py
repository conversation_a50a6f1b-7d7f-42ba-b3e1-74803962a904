#!/usr/bin/env python3
"""
FastAPI server to expose Yellow Pages scraping functionality
This wraps the existing main.py scraper with a REST API
"""

import asyncio
import json
from typing import List, Optional
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from dotenv import load_dotenv

# Import your existing scraper components
from crawl4ai import AsyncWebCrawler
from config import BASE_URL, CSS_SELECTOR, SCRAPER_INSTRUCTIONS
from src.scraper import (
    get_browser_config,
    get_llm_strategy,
    fetch_and_process_page
)
from models.business import BusinessData

load_dotenv()

app = FastAPI(title="Yellow Pages Scraper API", version="1.0.0")

# Enable CORS for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:3000"],  # React dev servers
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class ScrapeRequest(BaseModel):
    category: str
    location: str
    max_pages: Optional[int] = 3
    region: Optional[str] = "us"

class BusinessResponse(BaseModel):
    name: str
    address: Optional[str] = None
    phone: Optional[str] = None
    website: Optional[str] = None
    description: Optional[str] = None
    categories: Optional[List[str]] = None

class ScrapeResponse(BaseModel):
    businesses: List[BusinessResponse]
    total_found: int
    search_query: str
    location: str
    scraped_at: str

@app.get("/")
async def root():
    return {"message": "Yellow Pages Scraper API is running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "yellow-pages-scraper"}

@app.post("/scrape", response_model=ScrapeResponse)
async def scrape_businesses(request: ScrapeRequest):
    """
    Scrape businesses from Yellow Pages
    """
    try:
        # Initialize configurations
        browser_config = get_browser_config()
        llm_strategy = get_llm_strategy(
            llm_instructions=SCRAPER_INSTRUCTIONS,
            output_format=BusinessData
        )
        session_id = f"api_session_{request.category}_{request.location}"
        
        # Initialize state variables
        page_number = 1
        all_records = []
        seen_names = set()
        
        # Build search URL (adapt your BASE_URL logic)
        search_url = BASE_URL.format(
            category=request.category.replace(" ", "+"),
            location=request.location.replace(" ", "+")
        )
        
        # Start the web crawler
        async with AsyncWebCrawler(config=browser_config) as crawler:
            while page_number <= request.max_pages:
                # Fetch and process data from the current page
                records, no_results_found = await fetch_and_process_page(
                    crawler,
                    page_number,
                    search_url,
                    CSS_SELECTOR,
                    llm_strategy,
                    session_id,
                    seen_names,
                )
                
                if no_results_found or not records:
                    print(f"No more records found on page {page_number}. Ending crawl.")
                    break
                
                all_records.extend(records)
                page_number += 1
                
                # Add delay between pages
                await asyncio.sleep(1)
        
        # Transform records to API format
        businesses = []
        for record in all_records:
            business = BusinessResponse(
                name=getattr(record, 'name', ''),
                address=getattr(record, 'address', None),
                phone=getattr(record, 'phone', None),
                website=getattr(record, 'website', None),
                description=getattr(record, 'description', None),
                categories=getattr(record, 'categories', []) if hasattr(record, 'categories') else None
            )
            businesses.append(business)
        
        return ScrapeResponse(
            businesses=businesses,
            total_found=len(businesses),
            search_query=request.category,
            location=request.location,
            scraped_at=asyncio.get_event_loop().time().__str__()
        )
        
    except Exception as e:
        print(f"Scraping error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Scraping failed: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    print("Starting Yellow Pages Scraper API server...")
    print("Frontend can access at: http://localhost:8000")
    print("API docs available at: http://localhost:8000/docs")
    uvicorn.run(app, host="0.0.0.0", port=8000)
