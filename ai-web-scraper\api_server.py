#!/usr/bin/env python3
"""
FastAPI server to expose Yellow Pages scraping functionality
This wraps the existing main.py scraper with a REST API
"""

import asyncio
import json
from typing import List, Optional
from datetime import datetime
from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from dotenv import load_dotenv

# Import your existing scraper components
from crawl4ai import AsyncWebCrawler
from config import BASE_URL, CSS_SELECTOR, SCRAPER_INSTRUCTIONS
from src.scraper import (
    get_browser_config,
    get_llm_strategy,
    fetch_and_process_page
)
from models.business import BusinessData

load_dotenv()

app = FastAPI(title="Yellow Pages Scraper API", version="1.0.0")

# Enable CORS for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:3000", "http://127.0.0.1:5173"],  # React dev servers
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class ScrapeRequest(BaseModel):
    category: str
    location: str
    max_pages: Optional[int] = 3
    region: Optional[str] = "us"

class BusinessResponse(BaseModel):
    name: str
    address: Optional[str] = None
    phone: Optional[str] = None
    website: Optional[str] = None
    description: Optional[str] = None
    categories: Optional[List[str]] = None
    city: Optional[str] = None
    state: Optional[str] = None
    zip_code: Optional[str] = None
    rating: Optional[float] = None
    review_count: Optional[int] = None
    hours: Optional[dict] = None
    additional_info: Optional[dict] = None
    url: Optional[str] = None
    scraped_at: str

class ScrapeResponse(BaseModel):
    businesses: List[BusinessResponse]
    total_found: int
    page: int = 1
    per_page: int
    search_query: str
    location: Optional[str] = None
    has_more_pages: bool = False
    scraped_at: str

@app.get("/")
async def root():
    return {"message": "Yellow Pages Scraper API is running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "yellow-pages-scraper"}

@app.post("/scrape", response_model=ScrapeResponse)
async def scrape_businesses(request: ScrapeRequest):
    """
    Scrape businesses from Yellow Pages
    """
    try:
        # Initialize configurations
        browser_config = get_browser_config()
        llm_strategy = get_llm_strategy(
            llm_instructions=SCRAPER_INSTRUCTIONS,
            output_format=BusinessData
        )
        session_id = f"api_session_{request.category}_{request.location}"
        
        # Initialize state variables
        page_number = 1
        all_records = []
        seen_names = set()
        
        # Build search URL - handle both Canadian and US formats
        if request.region == "ca":
            # Canadian Yellow Pages format
            search_url = f"https://www.yellowpages.ca/search/si/{{page_number}}/{request.category.replace(' ', '+')}/{request.location.replace(' ', '+')}"
        else:
            # US Yellow Pages format
            search_url = f"https://www.yellowpages.com/search?search_terms={request.category.replace(' ', '+')}&geo_location_terms={request.location.replace(' ', '+')}&page={{page_number}}"
        
        # Start the web crawler
        async with AsyncWebCrawler(config=browser_config) as crawler:
            while page_number <= request.max_pages:
                # Fetch and process data from the current page
                records, no_results_found = await fetch_and_process_page(
                    crawler,
                    page_number,
                    search_url,
                    CSS_SELECTOR,
                    llm_strategy,
                    session_id,
                    seen_names,
                )
                
                if no_results_found or not records:
                    print(f"No more records found on page {page_number}. Ending crawl.")
                    break
                
                all_records.extend(records)
                page_number += 1
                
                # Add delay between pages
                await asyncio.sleep(1)
        
        # Transform records to API format
        businesses = []
        for record in all_records:
            # Handle both dict and object formats
            if isinstance(record, dict):
                business = BusinessResponse(
                    name=record.get('name', ''),
                    address=record.get('address'),
                    phone=record.get('phone_number') or record.get('phone'),
                    website=record.get('website'),
                    description=record.get('description'),
                    categories=[request.category] if request.category else None,
                    scraped_at=datetime.now().isoformat()
                )
            else:
                business = BusinessResponse(
                    name=getattr(record, 'name', ''),
                    address=getattr(record, 'address', None),
                    phone=getattr(record, 'phone_number', None) or getattr(record, 'phone', None),
                    website=getattr(record, 'website', None),
                    description=getattr(record, 'description', None),
                    categories=[request.category] if request.category else None,
                    scraped_at=datetime.now().isoformat()
                )
            businesses.append(business)

        return ScrapeResponse(
            businesses=businesses,
            total_found=len(businesses),
            per_page=len(businesses),
            search_query=request.category,
            location=request.location,
            has_more_pages=page_number <= request.max_pages and len(all_records) > 0,
            scraped_at=datetime.now().isoformat()
        )
        
    except Exception as e:
        print(f"Scraping error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Scraping failed: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    print("Starting Yellow Pages Scraper API server...")
    print("Frontend can access at: http://localhost:8003")
    print("API docs available at: http://localhost:8003/docs")
    uvicorn.run(app, host="0.0.0.0", port=8003)
