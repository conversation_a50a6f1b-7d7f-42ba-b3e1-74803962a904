{"root": ["../../src/app.test.tsx", "../../src/app.tsx", "../../src/main.tsx", "../../src/setuptests.ts", "../../src/vite-env.d.ts", "../../src/__tests__/setup.test.ts", "../../src/apis/google-places.ts", "../../src/apis/index.ts", "../../src/apis/overpass.ts", "../../src/apis/yellow-pages.ts", "../../src/apis/__tests__/google-places.test.ts", "../../src/apis/__tests__/overpass.test.ts", "../../src/apis/__tests__/yellow-pages.test.ts", "../../src/components/__tests__/dashboard.integration.test.tsx", "../../src/components/business/businesstable.tsx", "../../src/components/business/__tests__/businesstable.test.tsx", "../../src/components/dashboard/dashboard.tsx", "../../src/components/dashboard/tabpanel.tsx", "../../src/components/dashboard/__tests__/dashboard.test.tsx", "../../src/components/ui/errormessage.tsx", "../../src/components/ui/exportcontrols.tsx", "../../src/components/ui/loadingspinner.tsx", "../../src/components/ui/searchbar.tsx", "../../src/export/exporters.ts", "../../src/export/index.ts", "../../src/export/processors.ts", "../../src/export/transformers.ts", "../../src/export/__tests__/exporters.test.ts", "../../src/export/__tests__/processors.test.ts", "../../src/export/__tests__/transformers.test.ts", "../../src/hooks/usebusinesssearch.ts", "../../src/normalize/index.ts", "../../src/normalize/normalizer.ts", "../../src/normalize/__tests__/normalizer.test.ts", "../../src/services/businessservice.ts", "../../src/types/api-responses.ts", "../../src/types/business.ts", "../../src/types/common.ts", "../../src/types/index.ts", "../../src/types/validation.ts", "../../src/types/__tests__/api-responses.test.ts", "../../src/types/__tests__/business.test.ts", "../../src/types/__tests__/common.test.ts", "../../src/types/__tests__/validation.test.ts", "../../src/utils/rate-limiter.ts", "../../src/utils/test-utils.tsx", "../../src/utils/__tests__/test-utils.test.tsx"], "errors": true, "version": "5.8.3"}