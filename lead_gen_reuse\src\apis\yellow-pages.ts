/**
 * Yellow Pages API client - Browser-compatible version
 * Note: Full web scraping functionality requires a backend service
 * This version provides a compatible interface for the frontend
 */

import type { Coordinates } from '../types/common';
import { DataSource, BusinessStatus, ProcessingStatus } from '../types/common';
import type { YellowPages } from '../types/api-responses';
import type { ApiClientConfig, DataTransformer } from '../types';
import type { Business } from '../types/business';

export interface YellowPagesConfig extends ApiClientConfig {
  openaiApiKey?: string;
  model?: string;
  maxPages?: number;
  userAgent?: string;
}

export class YellowPagesError extends Error {
  constructor(
    message: string,
    public status?: number,
    public details?: unknown
  ) {
    super(message);
    this.name = 'YellowPagesError';
  }
}

/**
 * Browser-compatible Yellow Pages client
 * Note: Full web scraping functionality requires a backend service
 */
export class YellowPagesClient {
  private readonly config: YellowPagesConfig & {
    openaiApiKey: string;
    model: string;
    maxPages: number;
    userAgent: string;
  };

  constructor(config: YellowPagesConfig = {}) {
    this.config = {
      timeout: 30000,
      retries: 3,
      openaiApiKey: config.openaiApiKey || '',
      model: 'gpt-4o-mini',
      maxPages: 3,
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      ...config,
    };
  }

  /**
   * Search for businesses using Python backend service
   */
  async searchBusinesses(
    category: string,
    location: string,
    options: {
      maxPages?: number;
      region?: 'ca' | 'us';
      includeDetails?: boolean;
    } = {}
  ): Promise<YellowPages.SearchResponse> {
    const maxPages = options.maxPages || this.config.maxPages;
    const region = options.region || 'us';

    try {
      // Check if Python backend is running
      const backendUrl = 'http://localhost:8003'; // Default Python backend URL

      const response = await fetch(`${backendUrl}/scrape`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          category,
          location,
          max_pages: maxPages,
          region
        })
      });

      if (!response.ok) {
        if (response.status === 404 || !response.status) {
          throw new YellowPagesError(
            'Cannot connect to Python backend. Please start your scraper service:\n\n' +
            '1. cd ai-web-scraper\n' +
            '2. pip install fastapi uvicorn\n' +
            '3. Create api_server.py (see instructions below)\n' +
            '4. python api_server.py\n\n' +
            'Alternative: Use Google Places API or Overpass API for immediate results.',
            503,
            { category, location, options }
          );
        }
        throw new YellowPagesError(`Backend error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      return {
        businesses: data.businesses || [],
        total_found: data.total_found || 0,
        page: 1,
        per_page: data.businesses?.length || 0,
        search_query: category,
        location,
        has_more_pages: false,
        scraped_at: new Date().toISOString(),
      };

    } catch (error) {
      if (error instanceof YellowPagesError) {
        throw error;
      }

      // Network error - backend not running
      throw new YellowPagesError(
        'Cannot connect to Python backend. Please start your scraper service:\n\n' +
        '1. cd ai-web-scraper\n' +
        '2. pip install fastapi uvicorn\n' +
        '3. Create api_server.py (see instructions below)\n' +
        '4. python api_server.py\n\n' +
        'Alternative: Use Google Places API or Overpass API for immediate results.',
        503,
        {
          category,
          location,
          options,
          error: error instanceof Error ? error.message : String(error)
        }
      );
    }
  }

  /**
   * Get detailed business information - Browser-compatible version
   */
  async getBusinessDetails(businessUrl: string): Promise<YellowPages.ScrapedBusiness> {
    await new Promise(resolve => setTimeout(resolve, 500));

    throw new YellowPagesError(
      'Business details scraping requires a backend service.',
      501,
      { businessUrl }
    );
  }

  /**
   * Search near coordinates - Browser-compatible version
   */
  async searchNearCoordinates(
    category: string,
    coordinates: Coordinates,
    _radiusKm: number = 10,
    options: { maxPages?: number } = {}
  ): Promise<YellowPages.SearchResponse> {
    const location = `${coordinates.latitude},${coordinates.longitude}`;
    return this.searchBusinesses(category, location, options);
  }
}

/**
 * Transform Yellow Pages data to unified Business interface
 */
export const yellowPagesTransformer: DataTransformer<YellowPages.ScrapedBusiness, Business> = async (
  data: YellowPages.ScrapedBusiness
): Promise<Business> => {
  const id = `yp_${data.name.toLowerCase().replace(/[^a-z0-9]/g, '_')}_${Date.now()}`;

  return {
    id,
    name: data.name,
    address: {
      formatted_address: data.address || 'Address not available',
    },
    coordinates: undefined,
    contact: {
      phone: data.phone,
      website: data.website,
      email: undefined,
    },
    primary_type: data.category || data.categories?.[0],
    types: data.categories || (data.category ? [data.category] : []),
    business_status: BusinessStatus.OPERATIONAL,
    rating: data.rating ? {
      average: data.rating,
      total_reviews: data.reviewCount || 0,
      source: DataSource.YELLOW_PAGES,
    } : undefined,
    hours: undefined,
    source: DataSource.YELLOW_PAGES,
    source_id: data.url,
    raw_data: data,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    processing_status: ProcessingStatus.COMPLETED,
    data_quality: {
      completeness_score: 0.8,
      accuracy_score: 0.7,
      source_reliability: 0.6,
    },
  };
};
