/**
 * Google Places API search page with business type dropdowns
 */

import React, { useState } from 'react';
import { GOOGLE_PLACES_TYPES, getGooglePlacesOptions, type GooglePlacesType } from '../../constants/business-types';
import { GooglePlacesClient, googlePlacesTransformer } from '../../apis/google-places';

interface GooglePlacesSearchParams {
  businessTypes: GooglePlacesType[];
  location: string;
  radius: number;
  limit: number;
}

export const GooglePlacesPage: React.FC = () => {
  const [searchParams, setSearchParams] = useState<GooglePlacesSearchParams>({
    businessTypes: [],
    location: '',
    radius: 5000,
    limit: 50
  });
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);

  const businessTypeOptions = getGooglePlacesOptions();

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (searchParams.businessTypes.length === 0 || !searchParams.location) return;

    setLoading(true);
    setError(null);
    setResults([]);

    try {
      // Get API key from environment variables
      const apiKey = import.meta.env.VITE_GOOGLE_PLACES_API_KEY;
      if (!apiKey) {
        throw new Error('Google Places API key not found. Please set VITE_GOOGLE_PLACES_API_KEY in your environment variables.');
      }

      const client = new GooglePlacesClient({ apiKey });

      // Geocode the location first
      const geocodeResponse = await client.geocodeAddress(searchParams.location);
      if (!geocodeResponse.results || geocodeResponse.results.length === 0) {
        throw new Error('Location not found. Please try a different location.');
      }

      const location = geocodeResponse.results[0].geometry.location;
      const allResults: any[] = [];

      // Search for each business type
      for (const businessType of searchParams.businessTypes) {
        console.log(`Searching for ${businessType} near ${searchParams.location}...`);

        const searchResponse = await client.searchNearby(
          { latitude: location.lat, longitude: location.lng },
          {
            type: businessType,
            radius: searchParams.radius,
            maxResults: Math.min(20, Math.floor(searchParams.limit / searchParams.businessTypes.length)),
          }
        );

        if (searchResponse.places) {
          // Transform the results
          const transformedResults = await Promise.all(
            searchResponse.places.map(place => googlePlacesTransformer(place))
          );
          allResults.push(...transformedResults);
        }

        // Add a small delay between requests to respect rate limits
        if (searchParams.businessTypes.length > 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      // Remove duplicates and limit results
      const uniqueResults = allResults
        .filter((result, index, self) =>
          index === self.findIndex(r => r.id === result.id)
        )
        .slice(0, searchParams.limit);

      setResults(uniqueResults);
      console.log(`Found ${uniqueResults.length} unique businesses`);

    } catch (error) {
      console.error('Search failed:', error);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleBusinessTypeChange = (value: string) => {
    const businessType = value as GooglePlacesType;
    setSearchParams(prev => ({
      ...prev,
      businessTypes: prev.businessTypes.includes(businessType)
        ? prev.businessTypes.filter(type => type !== businessType)
        : [...prev.businessTypes, businessType]
    }));
  };

  const handleReset = () => {
    setSearchParams({
      businessTypes: [],
      location: '',
      radius: 5000,
      limit: 50
    });
    setResults([]);
    setError(null);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-white mb-4">
          🗺️ Google Places API Search
        </h2>
        <p className="text-gray-400 max-w-2xl mx-auto">
          Search for businesses using Google's comprehensive Places API. Get detailed information including 
          ratings, reviews, photos, and contact details.
        </p>
      </div>

      {/* Search Form */}
      <div className="bg-gradient-to-br from-gray-800 to-gray-700 rounded-2xl p-8 border border-gray-600 shadow-2xl">
        <form onSubmit={handleSearch} className="space-y-6">
          {/* Business Type Selection */}
          <div className="space-y-2">
            <label className="block text-sm font-semibold text-gray-300 mb-2">
              🏢 Business Types (Select Multiple)
            </label>

            {/* Selected Types Display */}
            {searchParams.businessTypes.length > 0 && (
              <div className="mb-4 p-3 bg-gray-700/30 rounded-lg border border-gray-600">
                <div className="flex flex-wrap gap-2">
                  {searchParams.businessTypes.map((type) => (
                    <span
                      key={type}
                      className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-500/20 text-blue-300 border border-blue-500/30"
                    >
                      {GOOGLE_PLACES_TYPES[type]}
                      <button
                        type="button"
                        onClick={() => handleBusinessTypeChange(type)}
                        className="ml-2 text-blue-400 hover:text-blue-200 transition-colors"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
                <div className="mt-2 text-xs text-gray-400">
                  {searchParams.businessTypes.length} type{searchParams.businessTypes.length !== 1 ? 's' : ''} selected
                </div>
              </div>
            )}

            {/* Business Type Categories */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto bg-gray-700/20 rounded-xl p-4 border border-gray-600">
              {Object.entries(businessTypeOptions).map(([category, options]) => (
                <div key={category} className="space-y-2">
                  <h4 className="font-semibold text-blue-300 text-sm border-b border-gray-600 pb-1">
                    {category}
                  </h4>
                  <div className="space-y-1">
                    {options.map(({ value, label }) => (
                      <label
                        key={value}
                        className="flex items-center space-x-2 text-sm cursor-pointer hover:bg-gray-600/30 rounded p-1 transition-colors"
                      >
                        <input
                          type="checkbox"
                          checked={searchParams.businessTypes.includes(value as GooglePlacesType)}
                          onChange={() => handleBusinessTypeChange(value)}
                          className="rounded border-gray-500 text-blue-500 focus:ring-blue-500 focus:ring-2"
                        />
                        <span className="text-gray-300 hover:text-white transition-colors">
                          {label}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Location and Parameters */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Location Input */}
            <div className="space-y-2">
              <label htmlFor="location" className="block text-sm font-semibold text-gray-300 mb-2">
                📍 Location
              </label>
              <div className="relative">
                <input
                  type="text"
                  id="location"
                  value={searchParams.location}
                  onChange={(e) => setSearchParams(prev => ({ ...prev, location: e.target.value }))}
                  placeholder="e.g., New York, NY or 123 Main St"
                  className="block w-full rounded-xl bg-gray-700/50 border-2 border-gray-600 text-white placeholder-gray-400 shadow-lg focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 sm:text-sm transition-all duration-300 py-4 px-4 backdrop-blur-sm"
                  required
                />
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-green-500/10 to-blue-500/10 pointer-events-none"></div>
              </div>
            </div>

            {/* Radius */}
            <div className="space-y-2">
              <label htmlFor="radius" className="block text-sm font-semibold text-gray-300 mb-2">
                🎯 Search Radius
              </label>
              <div className="relative">
                <select
                  id="radius"
                  value={searchParams.radius}
                  onChange={(e) => setSearchParams(prev => ({ ...prev, radius: parseInt(e.target.value) }))}
                  className="block w-full rounded-xl bg-gray-700/50 border-2 border-gray-600 text-white shadow-lg focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 sm:text-sm transition-all duration-300 py-4 px-4 backdrop-blur-sm appearance-none cursor-pointer"
                >
                  <option value={1000}>1 km radius</option>
                  <option value={2000}>2 km radius</option>
                  <option value={5000}>5 km radius</option>
                  <option value={10000}>10 km radius</option>
                  <option value={25000}>25 km radius</option>
                  <option value={50000}>50 km radius</option>
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Max Results */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <label htmlFor="limit" className="block text-sm font-semibold text-gray-300 mb-2">
                📊 Max Results
              </label>
              <div className="relative">
                <select
                  id="limit"
                  value={searchParams.limit}
                  onChange={(e) => setSearchParams(prev => ({ ...prev, limit: parseInt(e.target.value) }))}
                  className="block w-full rounded-xl bg-gray-700/50 border-2 border-gray-600 text-white shadow-lg focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 sm:text-sm transition-all duration-300 py-4 px-4 backdrop-blur-sm appearance-none cursor-pointer"
                >
                  <option value={10}>10 results</option>
                  <option value={25}>25 results</option>
                  <option value={50}>50 results</option>
                  <option value={100}>100 results</option>
                  <option value={200}>200 results</option>
                  <option value={500}>500 results</option>
                  <option value={1000}>1000 results</option>
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-center space-x-4 pt-4">
            <button
              type="submit"
              disabled={loading || searchParams.businessTypes.length === 0 || !searchParams.location}
              className="group relative inline-flex items-center px-8 py-4 border border-transparent text-sm font-bold rounded-xl shadow-lg text-white bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-4 focus:ring-blue-500/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
            >
              {loading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Searching...
                </>
              ) : (
                <>
                  <svg className="-ml-1 mr-3 h-5 w-5 group-hover:scale-110 transition-transform" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  Search Google Places
                </>
              )}
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500 to-purple-600 opacity-0 group-hover:opacity-20 transition-opacity"></div>
            </button>

            <button
              type="button"
              onClick={handleReset}
              disabled={loading}
              className="inline-flex items-center px-6 py-4 border-2 border-gray-600 text-sm font-semibold rounded-xl text-gray-300 bg-gray-700/50 hover:bg-gray-600/50 hover:border-gray-500 focus:outline-none focus:ring-4 focus:ring-gray-500/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 backdrop-blur-sm"
            >
              <svg className="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Reset
            </button>
          </div>
        </form>
      </div>

      {/* API Information */}
      <div className="bg-gradient-to-br from-blue-900/20 to-purple-900/20 rounded-2xl p-6 border border-blue-500/30">
        <h3 className="text-lg font-semibold text-white mb-3">
          📋 Google Places API Features
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="flex items-center space-x-2">
            <span className="text-green-400">✓</span>
            <span className="text-gray-300">Comprehensive business data</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-green-400">✓</span>
            <span className="text-gray-300">Ratings and reviews</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-green-400">✓</span>
            <span className="text-gray-300">Photos and images</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-green-400">✓</span>
            <span className="text-gray-300">Contact information</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-green-400">✓</span>
            <span className="text-gray-300">Opening hours</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-green-400">✓</span>
            <span className="text-gray-300">Price level indicators</span>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-900/20 border border-red-500/30 rounded-xl p-4">
          <div className="flex items-center space-x-2">
            <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-red-300 font-medium">Error</span>
          </div>
          <p className="text-red-200 mt-2">{error}</p>
        </div>
      )}

      {/* Results Display */}
      {results.length > 0 && (
        <div className="bg-gradient-to-br from-gray-800 to-gray-700 rounded-2xl p-8 border border-gray-600 shadow-2xl">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-2xl font-bold text-white">
              Search Results ({results.length})
            </h3>
            <button
              onClick={() => {
                const csv = convertToCSV(results);
                downloadCSV(csv, 'google-places-results.csv');
              }}
              className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Export CSV
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
            {results.map((business, index) => (
              <div key={business.id || index} className="bg-gray-700/50 rounded-lg p-4 border border-gray-600">
                <h4 className="font-semibold text-white mb-2">{business.name}</h4>
                <p className="text-gray-300 text-sm mb-2">{business.address}</p>
                {business.phone && (
                  <p className="text-blue-300 text-sm mb-1">📞 {business.phone}</p>
                )}
                {business.website && (
                  <a
                    href={business.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-400 hover:text-blue-300 text-sm underline"
                  >
                    🌐 Website
                  </a>
                )}
                {business.rating && (
                  <p className="text-yellow-400 text-sm mt-2">
                    ⭐ {business.rating} ({business.userRatingCount} reviews)
                  </p>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// Helper functions for CSV export
const convertToCSV = (data: any[]) => {
  if (data.length === 0) return '';

  const headers = ['Name', 'Address', 'Phone', 'Website', 'Rating', 'Primary Type'];
  const rows = data.map(business => [
    business.name || '',
    business.address || '',
    business.phone || '',
    business.website || '',
    business.rating || '',
    business.primaryType || ''
  ]);

  return [headers, ...rows].map(row =>
    row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(',')
  ).join('\n');
};

const downloadCSV = (csv: string, filename: string) => {
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
