# API keys for LLM providers - add key for every provider you want to use
OPENAI_API_KEY=""            # OpenAI API key for accessing OpenAI's models and services
GEMINI_API_KEY=""            # Google Cloud API key for accessing Google Cloud services
GROQ_API_KEY=""              # GROQ platform API key for using GROQ's services
ANTHROPIC_API_KEY=""         # Anthropic API key for Claude models

# LLM Model Configuration
LLM_MODEL="gemini/gemini-2.0-flash"  # Default model to use

# Scraper Configuration
MAX_PAGES=3                  # Maximum number of pages to scrape