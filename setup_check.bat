@echo off
title Lead Generation System - Setup Check
color 0E

echo.
echo ========================================
echo    LEAD GENERATION SYSTEM v2
echo         SETUP VERIFICATION
echo ========================================
echo.

echo Checking system requirements...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python is not installed or not in PATH
    echo Please install Python 3.11+ from https://python.org
    goto :error
) else (
    echo [OK] Python is installed
)

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org
    goto :error
) else (
    echo [OK] Node.js is installed
)

REM Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] npm is not installed
    goto :error
) else (
    echo [OK] npm is installed
)

echo.
echo Checking project files...
echo.

REM Check if ai-web-scraper directory exists
if not exist "ai-web-scraper" (
    echo [ERROR] ai-web-scraper directory not found
    goto :error
) else (
    echo [OK] ai-web-scraper directory found
)

REM Check if lead_gen_reuse directory exists
if not exist "lead_gen_reuse" (
    echo [ERROR] lead_gen_reuse directory not found
    goto :error
) else (
    echo [OK] lead_gen_reuse directory found
)

REM Check if .env file exists
if not exist "ai-web-scraper\.env" (
    echo [WARNING] .env file not found in ai-web-scraper
    echo You need to create this file with your API keys
    echo Copy .env.example to .env and add your keys
) else (
    echo [OK] .env file found
)

REM Check if Python dependencies are installed
cd ai-web-scraper
python -c "import fastapi, uvicorn, crawl4ai" >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARNING] Python dependencies not installed
    echo Run: pip install -r requirements.txt
) else (
    echo [OK] Python dependencies installed
)

cd ..

REM Check if Node.js dependencies are installed
if not exist "lead_gen_reuse\node_modules" (
    echo [WARNING] Node.js dependencies not installed
    echo Run: cd lead_gen_reuse && npm install
) else (
    echo [OK] Node.js dependencies installed
)

echo.
echo ========================================
echo    SETUP CHECK COMPLETE
echo ========================================
echo.
echo If all checks passed, you can run:
echo   start_leadgen_system.bat
echo.
echo If there were warnings, please address them first.
echo.
pause
exit /b 0

:error
echo.
echo ========================================
echo    SETUP INCOMPLETE
echo ========================================
echo.
echo Please fix the errors above before running the system.
echo.
pause
exit /b 1
