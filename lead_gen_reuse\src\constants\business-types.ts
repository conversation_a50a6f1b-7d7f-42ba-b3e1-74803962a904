/**
 * Business type constants for different APIs
 * Based on official API documentation
 */

// Google Places API Place Types (Table A) - Complete List
// https://developers.google.com/maps/documentation/places/web-service/place-types
export const GOOGLE_PLACES_TYPES = {
  // Food and Drink (Comprehensive)
  'acai_shop': 'Acai Shop',
  'afghani_restaurant': 'Afghan Restaurant',
  'african_restaurant': 'African Restaurant',
  'american_restaurant': 'American Restaurant',
  'asian_restaurant': 'Asian Restaurant',
  'bagel_shop': 'Bagel Shop',
  'bakery': 'Bakery',
  'bar': 'Bar',
  'bar_and_grill': 'Bar & Grill',
  'barbecue_restaurant': 'Barbecue Restaurant',
  'brazilian_restaurant': 'Brazilian Restaurant',
  'breakfast_restaurant': 'Breakfast Restaurant',
  'brunch_restaurant': 'Brunch Restaurant',
  'buffet_restaurant': 'Buffet Restaurant',
  'cafe': 'Cafe',
  'cafeteria': 'Cafeteria',
  'candy_store': 'Candy Store',
  'cat_cafe': 'Cat Cafe',
  'chinese_restaurant': 'Chinese Restaurant',
  'chocolate_factory': 'Chocolate Factory',
  'chocolate_shop': 'Chocolate Shop',
  'coffee_shop': 'Coffee Shop',
  'confectionery': 'Confectionery',
  'deli': 'Deli',
  'dessert_restaurant': 'Dessert Restaurant',
  'dessert_shop': 'Dessert Shop',
  'diner': 'Diner',
  'dog_cafe': 'Dog Cafe',
  'donut_shop': 'Donut Shop',
  'fast_food_restaurant': 'Fast Food Restaurant',
  'fine_dining_restaurant': 'Fine Dining Restaurant',
  'food_court': 'Food Court',
  'french_restaurant': 'French Restaurant',
  'greek_restaurant': 'Greek Restaurant',
  'hamburger_restaurant': 'Hamburger Restaurant',
  'ice_cream_shop': 'Ice Cream Shop',
  'indian_restaurant': 'Indian Restaurant',
  'indonesian_restaurant': 'Indonesian Restaurant',
  'italian_restaurant': 'Italian Restaurant',
  'japanese_restaurant': 'Japanese Restaurant',
  'juice_shop': 'Juice Shop',
  'korean_restaurant': 'Korean Restaurant',
  'lebanese_restaurant': 'Lebanese Restaurant',
  'meal_delivery': 'Meal Delivery',
  'meal_takeaway': 'Meal Takeaway',
  'mediterranean_restaurant': 'Mediterranean Restaurant',
  'mexican_restaurant': 'Mexican Restaurant',
  'middle_eastern_restaurant': 'Middle Eastern Restaurant',
  'pizza_restaurant': 'Pizza Restaurant',
  'pub': 'Pub',
  'ramen_restaurant': 'Ramen Restaurant',
  'restaurant': 'Restaurant',
  'sandwich_shop': 'Sandwich Shop',
  'seafood_restaurant': 'Seafood Restaurant',
  'spanish_restaurant': 'Spanish Restaurant',
  'steak_house': 'Steak House',
  'sushi_restaurant': 'Sushi Restaurant',
  'tea_house': 'Tea House',
  'thai_restaurant': 'Thai Restaurant',
  'turkish_restaurant': 'Turkish Restaurant',
  'vegan_restaurant': 'Vegan Restaurant',
  'vegetarian_restaurant': 'Vegetarian Restaurant',
  'vietnamese_restaurant': 'Vietnamese Restaurant',
  'wine_bar': 'Wine Bar',

  // Health and Wellness (Expanded)
  'chiropractor': 'Chiropractor',
  'dental_clinic': 'Dental Clinic',
  'dentist': 'Dentist',
  'doctor': 'Doctor',
  'drugstore': 'Drugstore',
  'hospital': 'Hospital',
  'massage': 'Massage Therapy',
  'medical_lab': 'Medical Lab',
  'pharmacy': 'Pharmacy',
  'physiotherapist': 'Physiotherapist',
  'sauna': 'Sauna',
  'skin_care_clinic': 'Skin Care Clinic',
  'spa': 'Spa',
  'tanning_studio': 'Tanning Studio',
  'veterinary_care': 'Veterinary Care',
  'wellness_center': 'Wellness Center',
  'yoga_studio': 'Yoga Studio',

  // Shopping (Comprehensive)
  'asian_grocery_store': 'Asian Grocery Store',
  'auto_parts_store': 'Auto Parts Store',
  'bicycle_store': 'Bicycle Store',
  'book_store': 'Book Store',
  'butcher_shop': 'Butcher Shop',
  'cell_phone_store': 'Cell Phone Store',
  'clothing_store': 'Clothing Store',
  'convenience_store': 'Convenience Store',
  'department_store': 'Department Store',
  'discount_store': 'Discount Store',
  'electronics_store': 'Electronics Store',
  'food_store': 'Food Store',
  'furniture_store': 'Furniture Store',
  'gift_shop': 'Gift Shop',
  'grocery_store': 'Grocery Store',
  'hardware_store': 'Hardware Store',
  'home_goods_store': 'Home Goods Store',
  'home_improvement_store': 'Home Improvement Store',
  'jewelry_store': 'Jewelry Store',
  'liquor_store': 'Liquor Store',
  'market': 'Market',
  'pet_store': 'Pet Store',
  'shoe_store': 'Shoe Store',
  'shopping_mall': 'Shopping Mall',
  'sporting_goods_store': 'Sporting Goods Store',
  'store': 'Store',
  'supermarket': 'Supermarket',
  'warehouse_store': 'Warehouse Store',
  'wholesaler': 'Wholesaler',

  // Services (Expanded)
  'accounting': 'Accounting',
  'astrologer': 'Astrologer',
  'atm': 'ATM',
  'bank': 'Bank',
  'barber_shop': 'Barber Shop',
  'beautician': 'Beautician',
  'beauty_salon': 'Beauty Salon',
  'body_art_service': 'Body Art Service',
  'catering_service': 'Catering Service',
  'cemetery': 'Cemetery',
  'child_care_agency': 'Child Care Agency',
  'consultant': 'Consultant',
  'courier_service': 'Courier Service',
  'electrician': 'Electrician',
  'florist': 'Florist',
  'food_delivery': 'Food Delivery',
  'foot_care': 'Foot Care',
  'funeral_home': 'Funeral Home',
  'hair_care': 'Hair Care',
  'hair_salon': 'Hair Salon',
  'insurance_agency': 'Insurance Agency',
  'laundry': 'Laundry',
  'lawyer': 'Lawyer',
  'locksmith': 'Locksmith',
  'makeup_artist': 'Makeup Artist',
  'moving_company': 'Moving Company',
  'nail_salon': 'Nail Salon',
  'painter': 'Painter',
  'plumber': 'Plumber',
  'psychic': 'Psychic',
  'real_estate_agency': 'Real Estate Agency',
  'roofing_contractor': 'Roofing Contractor',
  'storage': 'Storage',
  'summer_camp_organizer': 'Summer Camp Organizer',
  'tailor': 'Tailor',
  'telecommunications_service_provider': 'Telecommunications Service Provider',
  'tour_agency': 'Tour Agency',
  'tourist_information_center': 'Tourist Information Center',
  'travel_agency': 'Travel Agency',

  // Automotive (Expanded)
  'car_dealer': 'Car Dealer',
  'car_rental': 'Car Rental',
  'car_repair': 'Car Repair',
  'car_wash': 'Car Wash',
  'electric_vehicle_charging_station': 'EV Charging Station',
  'gas_station': 'Gas Station',
  'parking': 'Parking',
  'rest_stop': 'Rest Stop',

  // Business
  'corporate_office': 'Corporate Office',
  'farm': 'Farm',
  'ranch': 'Ranch',

  // Culture (Expanded)
  'art_gallery': 'Art Gallery',
  'art_studio': 'Art Studio',
  'auditorium': 'Auditorium',
  'cultural_landmark': 'Cultural Landmark',
  'historical_place': 'Historical Place',
  'monument': 'Monument',
  'museum': 'Museum',
  'performing_arts_theater': 'Performing Arts Theater',
  'sculpture': 'Sculpture',

  // Education (Expanded)
  'library': 'Library',
  'preschool': 'Preschool',
  'primary_school': 'Primary School',
  'school': 'School',
  'secondary_school': 'Secondary School',
  'university': 'University',

  // Entertainment and Recreation (Comprehensive)
  'adventure_sports_center': 'Adventure Sports Center',
  'amphitheatre': 'Amphitheatre',
  'amusement_center': 'Amusement Center',
  'amusement_park': 'Amusement Park',
  'aquarium': 'Aquarium',
  'banquet_hall': 'Banquet Hall',
  'barbecue_area': 'Barbecue Area',
  'botanical_garden': 'Botanical Garden',
  'bowling_alley': 'Bowling Alley',
  'casino': 'Casino',
  'childrens_camp': 'Children\'s Camp',
  'comedy_club': 'Comedy Club',
  'community_center': 'Community Center',
  'concert_hall': 'Concert Hall',
  'convention_center': 'Convention Center',
  'cultural_center': 'Cultural Center',
  'cycling_park': 'Cycling Park',
  'dance_hall': 'Dance Hall',
  'dog_park': 'Dog Park',
  'event_venue': 'Event Venue',
  'ferris_wheel': 'Ferris Wheel',
  'garden': 'Garden',
  'hiking_area': 'Hiking Area',
  'historical_landmark': 'Historical Landmark',
  'internet_cafe': 'Internet Cafe',
  'karaoke': 'Karaoke',
  'marina': 'Marina',
  'movie_rental': 'Movie Rental',
  'movie_theater': 'Movie Theater',
  'national_park': 'National Park',
  'night_club': 'Night Club',
  'observation_deck': 'Observation Deck',
  'off_roading_area': 'Off-Roading Area',
  'opera_house': 'Opera House',
  'park': 'Park',
  'philharmonic_hall': 'Philharmonic Hall',
  'picnic_ground': 'Picnic Ground',
  'planetarium': 'Planetarium',
  'plaza': 'Plaza',
  'roller_coaster': 'Roller Coaster',
  'skateboard_park': 'Skateboard Park',
  'state_park': 'State Park',
  'tourist_attraction': 'Tourist Attraction',
  'video_arcade': 'Video Arcade',
  'visitor_center': 'Visitor Center',
  'water_park': 'Water Park',
  'wedding_venue': 'Wedding Venue',
  'wildlife_park': 'Wildlife Park',
  'wildlife_refuge': 'Wildlife Refuge',
  'zoo': 'Zoo',

  // Facilities
  'public_bath': 'Public Bath',
  'public_bathroom': 'Public Bathroom',
  'stable': 'Stable',

  // Financial (Expanded)
  // Note: accounting, atm, bank already defined in Services section

  // Government (Expanded)
  'city_hall': 'City Hall',
  'courthouse': 'Courthouse',
  'embassy': 'Embassy',
  'fire_station': 'Fire Station',
  'government_office': 'Government Office',
  'local_government_office': 'Local Government Office',
  'police': 'Police Station',
  'post_office': 'Post Office',

  // Lodging (Comprehensive)
  'bed_and_breakfast': 'Bed & Breakfast',
  'budget_japanese_inn': 'Budget Japanese Inn',
  'campground': 'Campground',
  'camping_cabin': 'Camping Cabin',
  'cottage': 'Cottage',
  'extended_stay_hotel': 'Extended Stay Hotel',
  'farmstay': 'Farmstay',
  'guest_house': 'Guest House',
  'hostel': 'Hostel',
  'hotel': 'Hotel',
  'inn': 'Inn',
  'japanese_inn': 'Japanese Inn',
  'lodging': 'Lodging',
  'mobile_home_park': 'Mobile Home Park',
  'motel': 'Motel',
  'private_guest_room': 'Private Guest Room',
  'resort_hotel': 'Resort Hotel',
  'rv_park': 'RV Park',

  // Natural Features
  'beach': 'Beach',

  // Places of Worship
  'church': 'Church',
  'hindu_temple': 'Hindu Temple',
  'mosque': 'Mosque',
  'synagogue': 'Synagogue',

  // Sports (Comprehensive)
  'arena': 'Arena',
  'athletic_field': 'Athletic Field',
  'fishing_charter': 'Fishing Charter',
  'fishing_pond': 'Fishing Pond',
  'fitness_center': 'Fitness Center',
  'golf_course': 'Golf Course',
  'gym': 'Gym',
  'ice_skating_rink': 'Ice Skating Rink',
  'playground': 'Playground',
  'ski_resort': 'Ski Resort',
  'sports_activity_location': 'Sports Activity Location',
  'sports_club': 'Sports Club',
  'sports_coaching': 'Sports Coaching',
  'sports_complex': 'Sports Complex',
  'stadium': 'Stadium',
  'swimming_pool': 'Swimming Pool',

  // Transportation (Comprehensive)
  'airport': 'Airport',
  'airstrip': 'Airstrip',
  'bus_station': 'Bus Station',
  'bus_stop': 'Bus Stop',
  'ferry_terminal': 'Ferry Terminal',
  'heliport': 'Heliport',
  'international_airport': 'International Airport',
  'light_rail_station': 'Light Rail Station',
  'park_and_ride': 'Park & Ride',
  'subway_station': 'Subway Station',
  'taxi_stand': 'Taxi Stand',
  'train_station': 'Train Station',
  'transit_depot': 'Transit Depot',
  'transit_station': 'Transit Station',
  'truck_stop': 'Truck Stop',

  // Housing
  'apartment_building': 'Apartment Building',
  'apartment_complex': 'Apartment Complex',
  'condominium_complex': 'Condominium Complex',
  'housing_complex': 'Housing Complex',
} as const;

// OpenStreetMap Amenity Types - Complete List from Official Wiki
// https://wiki.openstreetmap.org/wiki/Key:amenity
export const OVERPASS_AMENITY_TYPES = {
  // Sustenance
  'bar': 'Bar',
  'biergarten': 'Beer Garden',
  'cafe': 'Cafe',
  'fast_food': 'Fast Food',
  'food_court': 'Food Court',
  'ice_cream': 'Ice Cream Shop',
  'pub': 'Pub',
  'restaurant': 'Restaurant',

  // Education
  'college': 'College',
  'dancing_school': 'Dancing School',
  'driving_school': 'Driving School',
  'first_aid_school': 'First Aid School',
  'kindergarten': 'Kindergarten',
  'language_school': 'Language School',
  'library': 'Library',
  'music_school': 'Music School',
  'research_institute': 'Research Institute',
  'school': 'School',
  'surf_school': 'Surf School',
  'toy_library': 'Toy Library',
  'traffic_park': 'Traffic Park',
  'training': 'Training Center',
  'university': 'University',

  // Transportation
  'bicycle_parking': 'Bicycle Parking',
  'bicycle_repair_station': 'Bicycle Repair Station',
  'bicycle_rental': 'Bicycle Rental',
  'bicycle_wash': 'Bicycle Wash',
  'boat_rental': 'Boat Rental',
  'boat_sharing': 'Boat Sharing',
  'bus_station': 'Bus Station',
  'car_rental': 'Car Rental',
  'car_sharing': 'Car Sharing',
  'car_wash': 'Car Wash',
  'charging_station': 'EV Charging Station',
  'compressed_air': 'Compressed Air',
  'driver_training': 'Driver Training',
  'ferry_terminal': 'Ferry Terminal',
  'fuel': 'Gas Station',
  'grit_bin': 'Grit Bin',
  'motorcycle_parking': 'Motorcycle Parking',
  'parking': 'Parking',
  'parking_entrance': 'Parking Entrance',
  'parking_space': 'Parking Space',
  'taxi': 'Taxi Stand',
  'vehicle_inspection': 'Vehicle Inspection',
  'weighbridge': 'Weighbridge',

  // Financial
  'atm': 'ATM',
  'bank': 'Bank',
  'bureau_de_change': 'Currency Exchange',
  'money_transfer': 'Money Transfer',
  'payment_centre': 'Payment Centre',
  'payment_terminal': 'Payment Terminal',

  // Healthcare
  'baby_hatch': 'Baby Hatch',
  'clinic': 'Clinic',
  'dentist': 'Dentist',
  'doctors': 'Doctor',
  'hospital': 'Hospital',
  'nursing_home': 'Nursing Home',
  'pharmacy': 'Pharmacy',
  'social_facility': 'Social Facility',
  'veterinary': 'Veterinary',

  // Entertainment, Arts & Culture
  'arts_centre': 'Arts Centre',
  'brothel': 'Brothel',
  'casino': 'Casino',
  'cinema': 'Cinema',
  'community_centre': 'Community Centre',
  'conference_centre': 'Conference Centre',
  'events_venue': 'Events Venue',
  'exhibition_centre': 'Exhibition Centre',
  'fountain': 'Fountain',
  'gambling': 'Gambling',
  'love_hotel': 'Love Hotel',
  'music_venue': 'Music Venue',
  'nightclub': 'Night Club',
  'planetarium': 'Planetarium',
  'public_bookcase': 'Public Bookcase',
  'social_centre': 'Social Centre',
  'stage': 'Stage',
  'stripclub': 'Strip Club',
  'studio': 'Studio',
  'swingerclub': 'Swinger Club',
  'theatre': 'Theatre',

  // Public Service
  'courthouse': 'Courthouse',
  'fire_station': 'Fire Station',
  'police': 'Police Station',
  'post_box': 'Post Box',
  'post_depot': 'Post Depot',
  'post_office': 'Post Office',
  'prison': 'Prison',
  'ranger_station': 'Ranger Station',
  'townhall': 'Town Hall',

  // Facilities
  'bbq': 'BBQ/Barbecue',
  'bench': 'Bench',
  'dog_toilet': 'Dog Toilet',
  'dressing_room': 'Dressing Room',
  'drinking_water': 'Drinking Water',
  'give_box': 'Give Box',
  'lounge': 'Lounge',
  'mailroom': 'Mailroom',
  'parcel_locker': 'Parcel Locker',
  'shelter': 'Shelter',
  'shower': 'Shower',
  'telephone': 'Public Phone',
  'toilets': 'Public Toilets',
  'water_point': 'Water Point',
  'watering_place': 'Watering Place',

  // Waste Management
  'recycling': 'Recycling',
  'sanitary_dump_station': 'Sanitary Dump Station',
  'waste_basket': 'Waste Basket',
  'waste_disposal': 'Waste Disposal',
  'waste_transfer_station': 'Waste Transfer Station',

  // Others
  'animal_boarding': 'Animal Boarding',
  'animal_breeding': 'Animal Breeding',
  'animal_shelter': 'Animal Shelter',
  'animal_training': 'Animal Training',
  'baking_oven': 'Baking Oven',
  'clock': 'Public Clock',
  'crematorium': 'Crematorium',
  'dive_centre': 'Dive Centre',
  'funeral_hall': 'Funeral Hall',
  'grave_yard': 'Graveyard',
  'hunting_stand': 'Hunting Stand',
  'internet_cafe': 'Internet Cafe',
  'kitchen': 'Public Kitchen',
  'kneipp_water_cure': 'Kneipp Water Cure',
  'lounger': 'Lounger',
  'marketplace': 'Marketplace',
  'monastery': 'Monastery',
  'mortuary': 'Mortuary',
  'photo_booth': 'Photo Booth',
  'place_of_mourning': 'Place of Mourning',
  'place_of_worship': 'Place of Worship',
  'public_bath': 'Public Bath',
  'public_building': 'Public Building',
  'refugee_site': 'Refugee Site',
  'vending_machine': 'Vending Machine',
} as const;

// Yellow Pages Categories - Comprehensive Business Directory Categories
export const YELLOW_PAGES_CATEGORIES = {
  // Food & Dining (Expanded)
  'restaurants': 'Restaurants',
  'pizza': 'Pizza',
  'chinese food': 'Chinese Food',
  'italian food': 'Italian Food',
  'mexican food': 'Mexican Food',
  'indian food': 'Indian Food',
  'thai food': 'Thai Food',
  'japanese food': 'Japanese Food',
  'korean food': 'Korean Food',
  'mediterranean food': 'Mediterranean Food',
  'fast food': 'Fast Food',
  'coffee shops': 'Coffee Shops',
  'bars': 'Bars & Pubs',
  'bakeries': 'Bakeries',
  'catering': 'Catering',
  'food trucks': 'Food Trucks',
  'delis': 'Delis',
  'seafood restaurants': 'Seafood Restaurants',
  'steakhouses': 'Steakhouses',
  'vegetarian restaurants': 'Vegetarian Restaurants',
  'vegan restaurants': 'Vegan Restaurants',
  'buffets': 'Buffets',
  'diners': 'Diners',
  'cafeterias': 'Cafeterias',

  // Health & Medical (Expanded)
  'doctors': 'Doctors',
  'dentists': 'Dentists',
  'hospitals': 'Hospitals',
  'pharmacies': 'Pharmacies',
  'veterinarians': 'Veterinarians',
  'chiropractors': 'Chiropractors',
  'physical therapy': 'Physical Therapy',
  'optometrists': 'Optometrists',
  'dermatologists': 'Dermatologists',
  'cardiologists': 'Cardiologists',
  'orthopedic surgeons': 'Orthopedic Surgeons',
  'pediatricians': 'Pediatricians',
  'psychiatrists': 'Psychiatrists',
  'psychologists': 'Psychologists',
  'medical labs': 'Medical Labs',
  'urgent care': 'Urgent Care',
  'nursing homes': 'Nursing Homes',
  'home health care': 'Home Health Care',
  'medical equipment': 'Medical Equipment',
  'massage therapy': 'Massage Therapy',

  // Professional Services (Expanded)
  'lawyers': 'Lawyers',
  'accountants': 'Accountants',
  'real estate': 'Real Estate',
  'insurance': 'Insurance',
  'banks': 'Banks',
  'financial services': 'Financial Services',
  'tax services': 'Tax Services',
  'bookkeeping': 'Bookkeeping',
  'business consultants': 'Business Consultants',
  'marketing agencies': 'Marketing Agencies',
  'advertising agencies': 'Advertising Agencies',
  'web design': 'Web Design',
  'graphic design': 'Graphic Design',
  'architects': 'Architects',
  'engineers': 'Engineers',
  'surveyors': 'Surveyors',
  'notaries': 'Notaries',
  'translators': 'Translators',
  'private investigators': 'Private Investigators',

  // Home & Garden (Expanded)
  'contractors': 'Contractors',
  'plumbers': 'Plumbers',
  'electricians': 'Electricians',
  'landscaping': 'Landscaping',
  'home improvement': 'Home Improvement',
  'cleaning services': 'Cleaning Services',
  'roofing': 'Roofing',
  'flooring': 'Flooring',
  'painting': 'Painting',
  'hvac': 'HVAC',
  'pest control': 'Pest Control',
  'tree services': 'Tree Services',
  'lawn care': 'Lawn Care',
  'pool services': 'Pool Services',
  'handyman': 'Handyman',
  'locksmiths': 'Locksmiths',
  'security systems': 'Security Systems',
  'moving companies': 'Moving Companies',
  'storage units': 'Storage Units',

  // Automotive (Expanded)
  'auto repair': 'Auto Repair',
  'car dealers': 'Car Dealers',
  'gas stations': 'Gas Stations',
  'auto parts': 'Auto Parts',
  'car wash': 'Car Wash',
  'tire shops': 'Tire Shops',
  'oil change': 'Oil Change',
  'auto body shops': 'Auto Body Shops',
  'motorcycle dealers': 'Motorcycle Dealers',
  'rv dealers': 'RV Dealers',
  'boat dealers': 'Boat Dealers',
  'auto insurance': 'Auto Insurance',
  'car rental': 'Car Rental',
  'towing services': 'Towing Services',

  // Beauty & Personal Care (Expanded)
  'hair salons': 'Hair Salons',
  'beauty salons': 'Beauty Salons',
  'barber shops': 'Barber Shops',
  'nail salons': 'Nail Salons',
  'spas': 'Spas',
  'massage spas': 'Massage Spas',
  'tanning salons': 'Tanning Salons',
  'tattoo parlors': 'Tattoo Parlors',
  'piercing studios': 'Piercing Studios',
  'cosmetics': 'Cosmetics',
  'skincare': 'Skincare',
  'weight loss centers': 'Weight Loss Centers',

  // Shopping & Retail (Expanded)
  'grocery stores': 'Grocery Stores',
  'clothing stores': 'Clothing Stores',
  'electronics': 'Electronics',
  'furniture': 'Furniture',
  'hardware stores': 'Hardware Stores',
  'pet stores': 'Pet Stores',
  'bookstores': 'Bookstores',
  'jewelry stores': 'Jewelry Stores',
  'shoe stores': 'Shoe Stores',
  'sporting goods': 'Sporting Goods',
  'toy stores': 'Toy Stores',
  'gift shops': 'Gift Shops',
  'antique stores': 'Antique Stores',
  'thrift stores': 'Thrift Stores',
  'department stores': 'Department Stores',
  'shopping malls': 'Shopping Malls',
  'convenience stores': 'Convenience Stores',
  'liquor stores': 'Liquor Stores',

  // Entertainment & Recreation (Expanded)
  'movie theaters': 'Movie Theaters',
  'gyms': 'Gyms & Fitness',
  'hotels': 'Hotels',
  'travel agencies': 'Travel Agencies',
  'amusement parks': 'Amusement Parks',
  'bowling alleys': 'Bowling Alleys',
  'golf courses': 'Golf Courses',
  'casinos': 'Casinos',
  'night clubs': 'Night Clubs',
  'museums': 'Museums',
  'theaters': 'Theaters',
  'concert venues': 'Concert Venues',
  'sports venues': 'Sports Venues',
  'recreation centers': 'Recreation Centers',
  'swimming pools': 'Swimming Pools',
  'tennis courts': 'Tennis Courts',
  'yoga studios': 'Yoga Studios',
  'dance studios': 'Dance Studios',

  // Education & Training
  'schools': 'Schools',
  'colleges': 'Colleges',
  'universities': 'Universities',
  'tutoring': 'Tutoring',
  'driving schools': 'Driving Schools',
  'music lessons': 'Music Lessons',
  'dance lessons': 'Dance Lessons',
  'martial arts': 'Martial Arts',
  'language schools': 'Language Schools',
  'computer training': 'Computer Training',
  'vocational schools': 'Vocational Schools',

  // Transportation & Logistics
  'taxi services': 'Taxi Services',
  'limousine services': 'Limousine Services',
  'bus services': 'Bus Services',
  'shipping services': 'Shipping Services',
  'courier services': 'Courier Services',
  'freight services': 'Freight Services',
  'logistics': 'Logistics',

  // Religious & Community
  'churches': 'Churches',
  'synagogues': 'Synagogues',
  'mosques': 'Mosques',
  'temples': 'Temples',
  'community centers': 'Community Centers',
  'senior centers': 'Senior Centers',
  'youth organizations': 'Youth Organizations',
  'charities': 'Charities',
  'non-profits': 'Non-Profits',
} as const;

// Type definitions for TypeScript
export type GooglePlacesType = keyof typeof GOOGLE_PLACES_TYPES;
export type OverpassAmenityType = keyof typeof OVERPASS_AMENITY_TYPES;
export type YellowPagesCategory = keyof typeof YELLOW_PAGES_CATEGORIES;

// Helper functions to get grouped options for dropdowns
export const getGooglePlacesOptions = () => {
  const grouped = {
    'Food & Drink': Object.entries(GOOGLE_PLACES_TYPES)
      .filter(([key]) =>
        key.includes('restaurant') || key.includes('cafe') || key.includes('bar') ||
        key.includes('food') || key === 'bakery' || key === 'pub' || key === 'biergarten' ||
        key.includes('shop') && (key.includes('coffee') || key.includes('ice_cream') ||
        key.includes('juice') || key.includes('tea') || key.includes('wine') ||
        key.includes('candy') || key.includes('chocolate') || key.includes('bagel') ||
        key.includes('donut') || key.includes('acai')) ||
        ['meal_delivery', 'meal_takeaway', 'deli', 'diner', 'cafeteria', 'confectionery'].includes(key)
      )
      .map(([value, label]) => ({ value, label })),

    'Health & Wellness': Object.entries(GOOGLE_PLACES_TYPES)
      .filter(([key]) =>
        ['hospital', 'pharmacy', 'dentist', 'doctor', 'dental_clinic', 'veterinary_care',
         'spa', 'gym', 'fitness_center', 'chiropractor', 'drugstore', 'massage',
         'medical_lab', 'physiotherapist', 'sauna', 'skin_care_clinic', 'tanning_studio',
         'wellness_center', 'yoga_studio'].includes(key)
      )
      .map(([value, label]) => ({ value, label })),

    'Shopping': Object.entries(GOOGLE_PLACES_TYPES)
      .filter(([key]) =>
        key.includes('store') || key.includes('shop') ||
        ['shopping_mall', 'supermarket', 'market', 'wholesaler'].includes(key)
      )
      .map(([value, label]) => ({ value, label })),

    'Services': Object.entries(GOOGLE_PLACES_TYPES)
      .filter(([key]) =>
        ['bank', 'atm', 'real_estate_agency', 'insurance_agency', 'lawyer', 'beauty_salon',
         'hair_salon', 'barber_shop', 'laundry', 'travel_agency', 'accounting', 'beautician',
         'body_art_service', 'catering_service', 'cemetery', 'child_care_agency', 'consultant',
         'courier_service', 'electrician', 'florist', 'food_delivery', 'foot_care',
         'funeral_home', 'hair_care', 'locksmith', 'makeup_artist', 'moving_company',
         'nail_salon', 'painter', 'plumber', 'psychic', 'roofing_contractor', 'storage',
         'summer_camp_organizer', 'tailor', 'telecommunications_service_provider',
         'tour_agency', 'tourist_information_center'].includes(key)
      )
      .map(([value, label]) => ({ value, label })),

    'Automotive': Object.entries(GOOGLE_PLACES_TYPES)
      .filter(([key]) =>
        key.includes('car_') || ['gas_station', 'parking', 'electric_vehicle_charging_station', 'rest_stop'].includes(key)
      )
      .map(([value, label]) => ({ value, label })),

    'Entertainment & Recreation': Object.entries(GOOGLE_PLACES_TYPES)
      .filter(([key]) =>
        ['amusement_park', 'aquarium', 'zoo', 'museum', 'art_gallery', 'movie_theater',
         'casino', 'night_club', 'bowling_alley', 'tourist_attraction', 'park', 'national_park',
         'adventure_sports_center', 'amphitheatre', 'amusement_center', 'banquet_hall',
         'barbecue_area', 'botanical_garden', 'childrens_camp', 'comedy_club', 'community_center',
         'concert_hall', 'convention_center', 'cultural_center', 'cycling_park', 'dance_hall',
         'dog_park', 'event_venue', 'ferris_wheel', 'garden', 'hiking_area', 'historical_landmark',
         'internet_cafe', 'karaoke', 'marina', 'movie_rental', 'observation_deck', 'off_roading_area',
         'opera_house', 'philharmonic_hall', 'picnic_ground', 'planetarium', 'plaza',
         'roller_coaster', 'skateboard_park', 'state_park', 'video_arcade', 'visitor_center',
         'water_park', 'wedding_venue', 'wildlife_park', 'wildlife_refuge'].includes(key)
      )
      .map(([value, label]) => ({ value, label })),

    'Culture & Arts': Object.entries(GOOGLE_PLACES_TYPES)
      .filter(([key]) =>
        ['art_gallery', 'art_studio', 'auditorium', 'cultural_landmark', 'historical_place',
         'monument', 'museum', 'performing_arts_theater', 'sculpture'].includes(key)
      )
      .map(([value, label]) => ({ value, label })),

    'Education': Object.entries(GOOGLE_PLACES_TYPES)
      .filter(([key]) =>
        ['school', 'university', 'library', 'preschool', 'primary_school', 'secondary_school'].includes(key)
      )
      .map(([value, label]) => ({ value, label })),

    'Lodging': Object.entries(GOOGLE_PLACES_TYPES)
      .filter(([key]) =>
        ['hotel', 'motel', 'lodging', 'bed_and_breakfast', 'campground', 'budget_japanese_inn',
         'camping_cabin', 'cottage', 'extended_stay_hotel', 'farmstay', 'guest_house', 'hostel',
         'inn', 'japanese_inn', 'mobile_home_park', 'private_guest_room', 'resort_hotel', 'rv_park'].includes(key)
      )
      .map(([value, label]) => ({ value, label })),

    'Sports & Fitness': Object.entries(GOOGLE_PLACES_TYPES)
      .filter(([key]) =>
        ['gym', 'fitness_center', 'arena', 'athletic_field', 'fishing_charter', 'fishing_pond',
         'golf_course', 'ice_skating_rink', 'playground', 'ski_resort', 'sports_activity_location',
         'sports_club', 'sports_coaching', 'sports_complex', 'stadium', 'swimming_pool'].includes(key)
      )
      .map(([value, label]) => ({ value, label })),

    'Transportation': Object.entries(GOOGLE_PLACES_TYPES)
      .filter(([key]) =>
        ['airport', 'airstrip', 'bus_station', 'bus_stop', 'ferry_terminal', 'heliport',
         'international_airport', 'light_rail_station', 'park_and_ride', 'subway_station',
         'taxi_stand', 'train_station', 'transit_depot', 'transit_station', 'truck_stop'].includes(key)
      )
      .map(([value, label]) => ({ value, label })),

    'Government & Public': Object.entries(GOOGLE_PLACES_TYPES)
      .filter(([key]) =>
        ['city_hall', 'courthouse', 'embassy', 'fire_station', 'government_office',
         'local_government_office', 'police', 'post_office'].includes(key)
      )
      .map(([value, label]) => ({ value, label })),

    'Places of Worship': Object.entries(GOOGLE_PLACES_TYPES)
      .filter(([key]) =>
        ['church', 'mosque', 'synagogue', 'hindu_temple'].includes(key)
      )
      .map(([value, label]) => ({ value, label })),

    'Business & Office': Object.entries(GOOGLE_PLACES_TYPES)
      .filter(([key]) =>
        ['corporate_office', 'farm', 'ranch'].includes(key)
      )
      .map(([value, label]) => ({ value, label })),

    'Housing': Object.entries(GOOGLE_PLACES_TYPES)
      .filter(([key]) =>
        ['apartment_building', 'apartment_complex', 'condominium_complex', 'housing_complex'].includes(key)
      )
      .map(([value, label]) => ({ value, label })),

    'Facilities': Object.entries(GOOGLE_PLACES_TYPES)
      .filter(([key]) =>
        ['public_bath', 'public_bathroom', 'stable', 'beach'].includes(key)
      )
      .map(([value, label]) => ({ value, label }))
  };

  return grouped;
};
