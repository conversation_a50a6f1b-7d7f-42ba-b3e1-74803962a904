/**
 * Simple client-side router for the Lead Generation Dashboard
 */

import React, { useState, createContext, useContext } from 'react';

// Route definitions
export type Route = 'google-places' | 'overpass' | 'yellow-pages';

interface RouterContextType {
  currentRoute: Route;
  navigate: (route: Route) => void;
}

const RouterContext = createContext<RouterContextType | null>(null);

export const useRouter = () => {
  const context = useContext(RouterContext);
  if (!context) {
    throw new Error('useRouter must be used within a RouterProvider');
  }
  return context;
};

interface RouterProviderProps {
  children: React.ReactNode;
  initialRoute?: Route;
}

export const RouterProvider: React.FC<RouterProviderProps> = ({ 
  children, 
  initialRoute = 'google-places' 
}) => {
  const [currentRoute, setCurrentRoute] = useState<Route>(initialRoute);

  const navigate = (route: Route) => {
    setCurrentRoute(route);
  };

  return (
    <RouterContext.Provider value={{ currentRoute, navigate }}>
      {children}
    </RouterContext.Provider>
  );
};

// Route configuration
export const ROUTES = {
  'google-places': {
    title: 'Google Places API',
    description: 'Search businesses using Google Places API with comprehensive place data',
    icon: '🗺️',
    color: 'from-blue-500 to-blue-600'
  },
  'overpass': {
    title: 'OpenStreetMap (Overpass)',
    description: 'Query OpenStreetMap data for businesses and points of interest',
    icon: '🌍',
    color: 'from-green-500 to-green-600'
  },
  'yellow-pages': {
    title: 'Yellow Pages Scraper',
    description: 'AI-powered scraping of Yellow Pages business directories',
    icon: '📞',
    color: 'from-yellow-500 to-orange-600'
  }
} as const;
