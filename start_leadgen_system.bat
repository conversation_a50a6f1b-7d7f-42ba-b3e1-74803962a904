@echo off
title Lead Generation System v2
color 0A

echo.
echo ========================================
echo    LEAD GENERATION SYSTEM v2
echo ========================================
echo.
echo Starting Python backend and React frontend...
echo.

echo Starting Python API server on port 8003...
start "Python API Server" cmd /k "cd /d %~dp0ai-web-scraper && python test_server.py"

timeout /t 2 /nobreak >nul

echo Starting React development server...
start "React Frontend" cmd /k "cd /d %~dp0lead_gen_reuse && npm run dev"

echo.
echo ========================================
echo    SERVICES STARTED!
echo ========================================
echo.
echo Dashboard: http://localhost:5173
echo API Docs:  http://localhost:8000/docs
echo.
echo Close both windows to stop services.
echo.
pause
