import React, { useState, useCallback } from 'react';
import type { SearchParams } from '../../types/common';

interface SearchBarProps {
  searchParams: SearchParams;
  onSearch: (params: SearchParams) => void;
  loading?: boolean;
}

export const SearchBar: React.FC<SearchBarProps> = ({
  searchParams,
  onSearch,
  loading = false
}) => {
  const [formData, setFormData] = useState<SearchParams>(searchParams);

  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.query.trim()) {
      return;
    }
    onSearch(formData);
  }, [formData, onSearch]);

  const handleInputChange = useCallback((
    field: keyof SearchParams,
    value: string | number
  ) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  }, []);

  const handleReset = useCallback(() => {
    const resetParams: SearchParams = {
      query: '',
      location: '',
      radius: 5000,
      limit: 50
    };
    setFormData(resetParams);
    onSearch(resetParams);
  }, [onSearch]);

  return (
    <div className="p-8">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Main Search Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Query Input */}
          <div className="space-y-2">
            <label htmlFor="query" className="block text-sm font-semibold text-gray-300 mb-2">
              🔍 Business Type or Name
            </label>
            <div className="relative">
              <input
                type="text"
                id="query"
                value={formData.query}
                onChange={(e) => handleInputChange('query', e.target.value)}
                placeholder="e.g., restaurants, coffee shops, dentists..."
                className="block w-full rounded-xl bg-gray-700/50 border-2 border-gray-600 text-white placeholder-gray-400 shadow-lg focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 sm:text-sm transition-all duration-300 py-4 px-4 backdrop-blur-sm"
                disabled={loading}
              />
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/10 to-purple-500/10 pointer-events-none"></div>
            </div>
          </div>

          {/* Location Input */}
          <div className="space-y-2">
            <label htmlFor="location" className="block text-sm font-semibold text-gray-300 mb-2">
              📍 Location
            </label>
            <div className="relative">
              <input
                type="text"
                id="location"
                value={formData.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
                placeholder="e.g., New York, NY or 123 Main St"
                className="block w-full rounded-xl bg-gray-700/50 border-2 border-gray-600 text-white placeholder-gray-400 shadow-lg focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 sm:text-sm transition-all duration-300 py-4 px-4 backdrop-blur-sm"
                disabled={loading}
              />
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-green-500/10 to-blue-500/10 pointer-events-none"></div>
            </div>
          </div>
        </div>

        {/* Advanced Options Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Radius */}
          <div className="space-y-2">
            <label htmlFor="radius" className="block text-sm font-semibold text-gray-300 mb-2">
              🎯 Search Radius
            </label>
            <div className="relative">
              <select
                id="radius"
                value={formData.radius}
                onChange={(e) => handleInputChange('radius', parseInt(e.target.value))}
                className="block w-full rounded-xl bg-gray-700/50 border-2 border-gray-600 text-white shadow-lg focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 sm:text-sm transition-all duration-300 py-4 px-4 backdrop-blur-sm appearance-none cursor-pointer"
                disabled={loading}
              >
                <option value={1000}>1 km radius</option>
                <option value={2000}>2 km radius</option>
                <option value={5000}>5 km radius</option>
                <option value={10000}>10 km radius</option>
                <option value={25000}>25 km radius</option>
                <option value={50000}>50 km radius</option>
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>
          </div>

          {/* Limit */}
          <div className="space-y-2">
            <label htmlFor="limit" className="block text-sm font-semibold text-gray-300 mb-2">
              📊 Max Results
            </label>
            <div className="relative">
              <select
                id="limit"
                value={formData.limit}
                onChange={(e) => handleInputChange('limit', parseInt(e.target.value))}
                className="block w-full rounded-xl bg-gray-700/50 border-2 border-gray-600 text-white shadow-lg focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 sm:text-sm transition-all duration-300 py-4 px-4 backdrop-blur-sm appearance-none cursor-pointer"
                disabled={loading}
              >
                <option value={10}>10 results</option>
                <option value={25}>25 results</option>
                <option value={50}>50 results</option>
                <option value={100}>100 results</option>
                <option value={200}>200 results</option>
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-center space-x-4 pt-4">

          {/* Action Buttons */}
          <div className="flex items-end space-x-2">
          <button
            type="submit"
            disabled={loading || !formData.query.trim()}
            className="group relative inline-flex items-center px-8 py-4 border border-transparent text-sm font-bold rounded-xl shadow-lg text-white bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-4 focus:ring-blue-500/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
          >
            {loading ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Searching...
              </>
            ) : (
              <>
                <svg className="-ml-1 mr-3 h-5 w-5 group-hover:scale-110 transition-transform" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                Search Businesses
              </>
            )}
            <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500 to-purple-600 opacity-0 group-hover:opacity-20 transition-opacity"></div>
          </button>

          <button
            type="button"
            onClick={handleReset}
            disabled={loading}
            className="inline-flex items-center px-6 py-4 border-2 border-gray-600 text-sm font-semibold rounded-xl text-gray-300 bg-gray-700/50 hover:bg-gray-600/50 hover:border-gray-500 focus:outline-none focus:ring-4 focus:ring-gray-500/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 backdrop-blur-sm"
          >
            <svg className="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Reset
          </button>
        </div>
        </div>
      </form>
    </div>
  );
};
