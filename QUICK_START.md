# 🚀 Lead Generation System v2 - Quick Start

## ⚡ One-Click Launch

**For Windows users**: Simply double-click `start_leadgen_system.bat` to launch both the backend and frontend!

## 📋 Prerequisites Check

Before running, ensure you have:

### Required Software
- ✅ **Python 3.11+** - [Download here](https://python.org)
- ✅ **Node.js 18+** - [Download here](https://nodejs.org)
- ✅ **Git** - [Download here](https://git-scm.com)

### API Keys Required
- 🔑 **OpenAI API Key** - Get from [OpenAI Platform](https://platform.openai.com/api-keys)

## 🔧 First-Time Setup

### 1. Install Dependencies

**Python Backend:**
```bash
cd ai-web-scraper
pip install -r requirements.txt
python -m playwright install
```

**React Frontend:**
```bash
cd lead_gen_reuse
npm install
```

### 2. Configure API Keys

1. Copy the example environment file:
   ```bash
   cd ai-web-scraper
   cp .env.example .env
   ```

2. Edit `.env` file and add your OpenAI API key:
   ```
   OPENAI_API_KEY=your_openai_api_key_here
   ```

### 3. Launch the System

**Option A: Windows Batch File (Recommended)**
```bash
start_leadgen_system.bat
```

**Option B: Manual Launch**
```bash
# Terminal 1 - Backend
cd ai-web-scraper
python api_server.py

# Terminal 2 - Frontend  
cd lead_gen_reuse
npm run dev
```

## 🌐 Access Points

Once running, access:
- **Dashboard**: http://localhost:5173
- **API Documentation**: http://localhost:8003/docs
- **Health Check**: http://localhost:8003/health

## 🔍 Features

### Data Sources
- ✅ **Google Places API** - Real business data
- ✅ **Overpass API** - OpenStreetMap business data  
- ✅ **Yellow Pages Scraper** - AI-powered web scraping (NEW!)

### Yellow Pages Scraper
- 🤖 **AI-Powered**: Uses OpenAI GPT-4o-mini for intelligent data extraction
- 🌍 **Multi-Region**: Supports US and Canadian Yellow Pages
- 📊 **Real Data**: Scrapes actual business listings with names, addresses, phones, websites
- ⚡ **Fast**: Concurrent processing with rate limiting

## 🛠️ Troubleshooting

### Common Issues

**"API token not configured"**
- Ensure your `.env` file exists in `ai-web-scraper/` directory
- Verify your OpenAI API key is correctly set

**"Cannot connect to Python backend"**
- Make sure the API server is running on port 8003
- Check if dependencies are installed: `pip install -r requirements.txt`

**"Playwright browser not found"**
- Run: `python -m playwright install`

**Port conflicts**
- Frontend: http://localhost:5173
- Backend: http://localhost:8003
- Make sure these ports are available

## 📁 Project Structure

```
leadgen_v2/
├── start_leadgen_system.bat    # One-click launcher
├── ai-web-scraper/            # Python backend (Yellow Pages)
│   ├── api_server.py          # Main API server
│   ├── .env                   # API keys (create from .env.example)
│   └── requirements.txt       # Python dependencies
└── lead_gen_reuse/           # React frontend
    ├── src/                  # Source code
    └── package.json          # Node.js dependencies
```

## 🎯 Usage

1. **Launch System**: Run `start_leadgen_system.bat`
2. **Open Dashboard**: Go to http://localhost:5173
3. **Select Data Source**: Choose from Google Places, Overpass, or Yellow Pages
4. **Enter Search**: Add keywords, location, and filters
5. **Export Results**: Download as CSV

The Yellow Pages scraper now returns **real scraped data** instead of mock data! 🎉
