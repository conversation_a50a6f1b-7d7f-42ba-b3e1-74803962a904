/**
 * React hook for managing business search state and operations
 */

import { useState, useCallback } from 'react';
import type { Business } from '../types/business';
import type { SearchParams } from '../types/common';
import { DataSource } from '../types/common';
import { businessService, type SearchResult } from '../services/businessService';

export interface BusinessSearchState {
  // Data for each source
  [DataSource.GOOGLE_PLACES]: Business[];
  [DataSource.OVERPASS]: Business[];
  [DataSource.YELLOW_PAGES]: Business[];

  // Loading states
  loading: {
    [DataSource.GOOGLE_PLACES]: boolean;
    [DataSource.OVERPASS]: boolean;
    [DataSource.YELLOW_PAGES]: boolean;
  };

  // Error states
  errors: {
    [DataSource.GOOGLE_PLACES]: string | null;
    [DataSource.OVERPASS]: string | null;
    [DataSource.YELLOW_PAGES]: string | null;
  };

  // Search metadata
  hasMore: {
    [DataSource.GOOGLE_PLACES]: boolean;
    [DataSource.OVERPASS]: boolean;
    [DataSource.YELLOW_PAGES]: boolean;
  };

  // Last search parameters
  lastSearch: SearchParams | null;
}

const initialState: BusinessSearchState = {
  [DataSource.GOOGLE_PLACES]: [],
  [DataSource.OVERPASS]: [],
  [DataSource.YELLOW_PAGES]: [],
  loading: {
    [DataSource.GOOGLE_PLACES]: false,
    [DataSource.OVERPASS]: false,
    [DataSource.YELLOW_PAGES]: false,
  },
  errors: {
    [DataSource.GOOGLE_PLACES]: null,
    [DataSource.OVERPASS]: null,
    [DataSource.YELLOW_PAGES]: null,
  },
  hasMore: {
    [DataSource.GOOGLE_PLACES]: false,
    [DataSource.OVERPASS]: false,
    [DataSource.YELLOW_PAGES]: false,
  },
  lastSearch: null,
};

export function useBusinessSearch() {
  const [state, setState] = useState<BusinessSearchState>(initialState);

  // Search a specific source
  const searchSource = useCallback(async (source: DataSource, params: SearchParams) => {
    setState(prev => ({
      ...prev,
      loading: { ...prev.loading, [source]: true },
      errors: { ...prev.errors, [source]: null },
      lastSearch: params,
    }));

    try {
      let result: SearchResult;
      
      switch (source) {
        case DataSource.GOOGLE_PLACES:
          result = await businessService.searchGooglePlaces(params);
          break;
        case DataSource.OVERPASS:
          result = await businessService.searchOverpass(params);
          break;
        case DataSource.YELLOW_PAGES:
          result = await businessService.searchYellowPages(params);
          break;
        default:
          throw new Error(`Unknown source: ${source}`);
      }

      setState(prev => ({
        ...prev,
        [source]: result.businesses,
        loading: { ...prev.loading, [source]: false },
        hasMore: { ...prev.hasMore, [source]: result.hasMore },
      }));

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Search failed';
      
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, [source]: false },
        errors: { ...prev.errors, [source]: errorMessage },
      }));
      
      throw error;
    }
  }, []);

  // Search all sources
  const searchAll = useCallback(async (params: SearchParams) => {
    setState(prev => ({
      ...prev,
      loading: {
        googlePlaces: true,
        overpass: true,
        yellowPages: true,
      },
      errors: {
        googlePlaces: null,
        overpass: null,
        yellowPages: null,
      },
      lastSearch: params,
    }));

    try {
      const results = await businessService.searchAll(params);

      setState(prev => ({
        ...prev,
        [DataSource.GOOGLE_PLACES]: results.googlePlaces?.businesses || [],
        [DataSource.OVERPASS]: results.overpass?.businesses || [],
        [DataSource.YELLOW_PAGES]: results.yellowPages?.businesses || [],
        loading: {
          [DataSource.GOOGLE_PLACES]: false,
          [DataSource.OVERPASS]: false,
          [DataSource.YELLOW_PAGES]: false,
        },
        hasMore: {
          [DataSource.GOOGLE_PLACES]: results.googlePlaces?.hasMore || false,
          [DataSource.OVERPASS]: results.overpass?.hasMore || false,
          [DataSource.YELLOW_PAGES]: results.yellowPages?.hasMore || false,
        },
        errors: {
          [DataSource.GOOGLE_PLACES]: results.errors.find(e => e.source === DataSource.GOOGLE_PLACES)?.message || null,
          [DataSource.OVERPASS]: results.errors.find(e => e.source === DataSource.OVERPASS)?.message || null,
          [DataSource.YELLOW_PAGES]: results.errors.find(e => e.source === DataSource.YELLOW_PAGES)?.message || null,
        },
      }));

      return results;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Search failed';
      
      setState(prev => ({
        ...prev,
        loading: {
          [DataSource.GOOGLE_PLACES]: false,
          [DataSource.OVERPASS]: false,
          [DataSource.YELLOW_PAGES]: false,
        },
        errors: {
          [DataSource.GOOGLE_PLACES]: errorMessage,
          [DataSource.OVERPASS]: errorMessage,
          [DataSource.YELLOW_PAGES]: errorMessage,
        },
      }));
      
      throw error;
    }
  }, []);

  // Clear data for a specific source
  const clearSource = useCallback((source: DataSource) => {
    setState(prev => ({
      ...prev,
      [source]: [],
      errors: { ...prev.errors, [source]: null },
      hasMore: { ...prev.hasMore, [source]: false },
    }));
  }, []);

  // Clear all data
  const clearAll = useCallback(() => {
    setState(initialState);
  }, []);

  // Get data for a specific source
  const getSourceData = useCallback((source: DataSource) => {
    return {
      businesses: state[source],
      loading: state.loading[source],
      error: state.errors[source],
      hasMore: state.hasMore[source],
    };
  }, [state]);

  return {
    state,
    searchSource,
    searchAll,
    clearSource,
    clearAll,
    getSourceData,
  };
}
