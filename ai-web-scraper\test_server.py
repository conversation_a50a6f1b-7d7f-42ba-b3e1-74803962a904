#!/usr/bin/env python3
"""
Minimal test server to verify Python backend works
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

app = FastAPI(title="Test API", version="1.0.0")

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "OPTIONS"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Test server is working!"}

@app.get("/health")
async def health():
    return {"status": "healthy", "service": "test-server"}

@app.options("/scrape")
async def scrape_options():
    return {"message": "OK"}

@app.post("/scrape")
async def scrape_businesses(request: dict):
    """Mock scrape endpoint for testing"""
    import asyncio
    await asyncio.sleep(1)  # Simulate processing

    category = request.get("category", "business")
    location = request.get("location", "Unknown")

    mock_businesses = [
        {
            "name": f"Sample {category} Business 1",
            "address": f"123 Main St, {location}",
            "phone": "(*************",
            "website": "https://example1.com",
            "description": f"A great {category} business in {location}",
            "category": category,
            "rating": 4.5,
            "reviewCount": 127
        },
        {
            "name": f"Sample {category} Business 2",
            "address": f"456 Oak Ave, {location}",
            "phone": "(*************",
            "website": "https://example2.com",
            "description": f"Another excellent {category} service",
            "category": category,
            "rating": 4.2,
            "reviewCount": 89
        },
        {
            "name": f"Premium {category} Co.",
            "address": f"789 Pine Rd, {location}",
            "phone": "(*************",
            "website": "https://premium-example.com",
            "description": f"Premium {category} services since 1995",
            "category": category,
            "rating": 4.8,
            "reviewCount": 203
        }
    ]

    return {
        "businesses": mock_businesses,
        "total_found": len(mock_businesses),
        "search_query": category,
        "location": location,
        "scraped_at": "2025-01-16T20:00:00Z"
    }

if __name__ == "__main__":
    print("Starting test server on http://localhost:8003")
    uvicorn.run(app, host="0.0.0.0", port=8003)
