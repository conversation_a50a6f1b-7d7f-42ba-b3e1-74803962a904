/**
 * Yellow Pages scraper page with business category suggestions
 */

import React, { useState } from 'react';
import { YELLOW_PAGES_CATEGORIES, type YellowPagesCategory } from '../../constants/business-types';
import { YellowPagesClient, yellowPagesTransformer } from '../../apis/yellow-pages';
import type { Business } from '../../types/business';

interface YellowPagesSearchParams {
  businessCategories: YellowPagesCategory[];
  customQuery: string;
  location: string;
  region: 'us' | 'ca';
  maxPages: number;
}

export const YellowPagesPage: React.FC = () => {
  const [searchParams, setSearchParams] = useState<YellowPagesSearchParams>({
    businessCategories: [],
    customQuery: '',
    location: '',
    region: 'us',
    maxPages: 3
  });
  const [loading, setLoading] = useState(false);
  const [useCustomQuery, setUseCustomQuery] = useState(false);
  const [results, setResults] = useState<Business[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Group categories using comprehensive list
  const categoryGroups = {
    'Food & Dining': ['restaurants', 'pizza', 'chinese food', 'italian food', 'mexican food', 'indian food', 'thai food', 'japanese food', 'korean food', 'mediterranean food', 'fast food', 'coffee shops', 'bars', 'bakeries', 'catering', 'food trucks', 'delis', 'seafood restaurants', 'steakhouses', 'vegetarian restaurants', 'vegan restaurants', 'buffets', 'diners', 'cafeterias'],
    'Health & Medical': ['doctors', 'dentists', 'hospitals', 'pharmacies', 'veterinarians', 'chiropractors', 'physical therapy', 'optometrists', 'dermatologists', 'cardiologists', 'orthopedic surgeons', 'pediatricians', 'psychiatrists', 'psychologists', 'medical labs', 'urgent care', 'nursing homes', 'home health care', 'medical equipment', 'massage therapy'],
    'Professional Services': ['lawyers', 'accountants', 'real estate', 'insurance', 'banks', 'financial services', 'tax services', 'bookkeeping', 'business consultants', 'marketing agencies', 'advertising agencies', 'web design', 'graphic design', 'architects', 'engineers', 'surveyors', 'notaries', 'translators', 'private investigators'],
    'Home & Garden': ['contractors', 'plumbers', 'electricians', 'landscaping', 'home improvement', 'cleaning services', 'roofing', 'flooring', 'painting', 'hvac', 'pest control', 'tree services', 'lawn care', 'pool services', 'handyman', 'locksmiths', 'security systems', 'moving companies', 'storage units'],
    'Automotive': ['auto repair', 'car dealers', 'gas stations', 'auto parts', 'car wash', 'tire shops', 'oil change', 'auto body shops', 'motorcycle dealers', 'rv dealers', 'boat dealers', 'auto insurance', 'car rental', 'towing services'],
    'Beauty & Personal Care': ['hair salons', 'beauty salons', 'barber shops', 'nail salons', 'spas', 'massage spas', 'tanning salons', 'tattoo parlors', 'piercing studios', 'cosmetics', 'skincare', 'weight loss centers'],
    'Shopping & Retail': ['grocery stores', 'clothing stores', 'electronics', 'furniture', 'hardware stores', 'pet stores', 'bookstores', 'jewelry stores', 'shoe stores', 'sporting goods', 'toy stores', 'gift shops', 'antique stores', 'thrift stores', 'department stores', 'shopping malls', 'convenience stores', 'liquor stores'],
    'Entertainment & Recreation': ['movie theaters', 'gyms', 'hotels', 'travel agencies', 'amusement parks', 'bowling alleys', 'golf courses', 'casinos', 'night clubs', 'museums', 'theaters', 'concert venues', 'sports venues', 'recreation centers', 'swimming pools', 'tennis courts', 'yoga studios', 'dance studios'],
    'Education & Training': ['schools', 'colleges', 'universities', 'tutoring', 'driving schools', 'music lessons', 'dance lessons', 'martial arts', 'language schools', 'computer training', 'vocational schools'],
    'Transportation & Logistics': ['taxi services', 'limousine services', 'bus services', 'shipping services', 'courier services', 'freight services', 'logistics'],
    'Religious & Community': ['churches', 'synagogues', 'mosques', 'temples', 'community centers', 'senior centers', 'youth organizations', 'charities', 'non-profits']
  };

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    const query = useCustomQuery ? searchParams.customQuery : searchParams.businessCategories.join(', ');
    if (!query || !searchParams.location) return;

    setLoading(true);
    setError(null);
    setResults([]);

    try {
      // Get OpenAI API key from environment variables
      const openaiApiKey = import.meta.env.VITE_OPENAI_API_KEY;
      if (!openaiApiKey) {
        throw new Error('OpenAI API key not found. Please set VITE_OPENAI_API_KEY in your environment variables for AI-powered Yellow Pages scraping.');
      }

      const client = new YellowPagesClient({
        openaiApiKey,
        timeout: 60000, // Yellow Pages scraping can take longer
        requestsPerSecond: 0.5, // Very conservative rate limit
      });

      console.log(`Searching Yellow Pages for "${query}" in ${searchParams.location}...`);

      const searchResponse = await client.searchBusinesses(
        query,
        searchParams.location,
        {
          maxPages: searchParams.maxPages,
          region: searchParams.region,
          includeDetails: true
        }
      );

      if (searchResponse.businesses && searchResponse.businesses.length > 0) {
        // Transform the results
        const transformedResults = await Promise.all(
          searchResponse.businesses.map(business => yellowPagesTransformer(business))
        );

        setResults(transformedResults);
        console.log(`Found ${transformedResults.length} businesses from Yellow Pages`);
      } else {
        console.log('No businesses found');
        setResults([]);
      }

    } catch (error) {
      console.error('Search failed:', error);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleBusinessCategoryChange = (value: string) => {
    const businessCategory = value as YellowPagesCategory;
    setSearchParams(prev => ({
      ...prev,
      businessCategories: prev.businessCategories.includes(businessCategory)
        ? prev.businessCategories.filter(cat => cat !== businessCategory)
        : [...prev.businessCategories, businessCategory]
    }));
  };

  const handleReset = () => {
    setSearchParams({
      businessCategories: [],
      customQuery: '',
      location: '',
      region: 'us',
      maxPages: 3
    });
    setUseCustomQuery(false);
    setResults([]);
    setError(null);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-white mb-4">
          📞 Yellow Pages AI Scraper
        </h2>
        <p className="text-gray-400 max-w-2xl mx-auto">
          AI-powered scraping of Yellow Pages business directories. Extract comprehensive business 
          information from both US and Canadian Yellow Pages.
        </p>
      </div>

      {/* Search Form */}
      <div className="bg-gradient-to-br from-gray-800 to-gray-700 rounded-2xl p-8 border border-gray-600 shadow-2xl">
        <form onSubmit={handleSearch} className="space-y-6">
          {/* Query Type Toggle */}
          <div className="flex items-center justify-center space-x-4 mb-6">
            <button
              type="button"
              onClick={() => setUseCustomQuery(false)}
              className={`px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                !useCustomQuery 
                  ? 'bg-gradient-to-r from-yellow-500 to-orange-600 text-white shadow-lg' 
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              📋 Use Category
            </button>
            <button
              type="button"
              onClick={() => setUseCustomQuery(true)}
              className={`px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                useCustomQuery 
                  ? 'bg-gradient-to-r from-yellow-500 to-orange-600 text-white shadow-lg' 
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              ✏️ Custom Query
            </button>
          </div>

          {/* Business Category or Custom Query */}
          {!useCustomQuery ? (
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-300 mb-2">
                🏢 Business Categories (Select Multiple)
              </label>

              {/* Selected Categories Display */}
              {searchParams.businessCategories.length > 0 && (
                <div className="mb-4 p-3 bg-gray-700/30 rounded-lg border border-gray-600">
                  <div className="flex flex-wrap gap-2">
                    {searchParams.businessCategories.map((category) => (
                      <span
                        key={category}
                        className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-500/20 text-yellow-300 border border-yellow-500/30"
                      >
                        {YELLOW_PAGES_CATEGORIES[category]}
                        <button
                          type="button"
                          onClick={() => handleBusinessCategoryChange(category)}
                          className="ml-2 text-yellow-400 hover:text-yellow-200 transition-colors"
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                  <div className="mt-2 text-xs text-gray-400">
                    {searchParams.businessCategories.length} categor{searchParams.businessCategories.length !== 1 ? 'ies' : 'y'} selected
                  </div>
                </div>
              )}

              {/* Business Category Selection */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto bg-gray-700/20 rounded-xl p-4 border border-gray-600">
                {Object.entries(categoryGroups).map(([category, types]) => (
                  <div key={category} className="space-y-2">
                    <h4 className="font-semibold text-yellow-300 text-sm border-b border-gray-600 pb-1">
                      {category}
                    </h4>
                    <div className="space-y-1">
                      {types.map((type) => (
                        <label
                          key={type}
                          className="flex items-center space-x-2 text-sm cursor-pointer hover:bg-gray-600/30 rounded p-1 transition-colors"
                        >
                          <input
                            type="checkbox"
                            checked={searchParams.businessCategories.includes(type as YellowPagesCategory)}
                            onChange={() => handleBusinessCategoryChange(type)}
                            className="rounded border-gray-500 text-yellow-500 focus:ring-yellow-500 focus:ring-2"
                          />
                          <span className="text-gray-300 hover:text-white transition-colors">
                            {YELLOW_PAGES_CATEGORIES[type as YellowPagesCategory]}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <label htmlFor="customQuery" className="block text-sm font-semibold text-gray-300 mb-2">
                ✏️ Custom Search Query
              </label>
              <div className="relative">
                <input
                  type="text"
                  id="customQuery"
                  value={searchParams.customQuery}
                  onChange={(e) => setSearchParams(prev => ({ ...prev, customQuery: e.target.value }))}
                  placeholder="e.g., organic restaurants, 24-hour pharmacies, luxury hotels..."
                  className="block w-full rounded-xl bg-gray-700/50 border-2 border-gray-600 text-white placeholder-gray-400 shadow-lg focus:border-yellow-500 focus:ring-4 focus:ring-yellow-500/20 sm:text-sm transition-all duration-300 py-4 px-4 backdrop-blur-sm"
                  required={useCustomQuery}
                />
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-yellow-500/10 to-orange-500/10 pointer-events-none"></div>
              </div>
            </div>
          )}

          {/* Location and Region */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Location Input */}
            <div className="space-y-2">
              <label htmlFor="location" className="block text-sm font-semibold text-gray-300 mb-2">
                📍 Location
              </label>
              <div className="relative">
                <input
                  type="text"
                  id="location"
                  value={searchParams.location}
                  onChange={(e) => setSearchParams(prev => ({ ...prev, location: e.target.value }))}
                  placeholder="e.g., New York, NY or Toronto, ON"
                  className="block w-full rounded-xl bg-gray-700/50 border-2 border-gray-600 text-white placeholder-gray-400 shadow-lg focus:border-yellow-500 focus:ring-4 focus:ring-yellow-500/20 sm:text-sm transition-all duration-300 py-4 px-4 backdrop-blur-sm"
                  required
                />
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-green-500/10 to-blue-500/10 pointer-events-none"></div>
              </div>
            </div>

            {/* Region */}
            <div className="space-y-2">
              <label htmlFor="region" className="block text-sm font-semibold text-gray-300 mb-2">
                🌎 Region
              </label>
              <div className="relative">
                <select
                  id="region"
                  value={searchParams.region}
                  onChange={(e) => setSearchParams(prev => ({ ...prev, region: e.target.value as 'us' | 'ca' }))}
                  className="block w-full rounded-xl bg-gray-700/50 border-2 border-gray-600 text-white shadow-lg focus:border-yellow-500 focus:ring-4 focus:ring-yellow-500/20 sm:text-sm transition-all duration-300 py-4 px-4 backdrop-blur-sm appearance-none cursor-pointer"
                >
                  <option value="us">🇺🇸 United States</option>
                  <option value="ca">🇨🇦 Canada</option>
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Max Pages */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <label htmlFor="maxPages" className="block text-sm font-semibold text-gray-300 mb-2">
                📄 Max Pages to Scrape
              </label>
              <div className="relative">
                <select
                  id="maxPages"
                  value={searchParams.maxPages}
                  onChange={(e) => setSearchParams(prev => ({ ...prev, maxPages: parseInt(e.target.value) }))}
                  className="block w-full rounded-xl bg-gray-700/50 border-2 border-gray-600 text-white shadow-lg focus:border-yellow-500 focus:ring-4 focus:ring-yellow-500/20 sm:text-sm transition-all duration-300 py-4 px-4 backdrop-blur-sm appearance-none cursor-pointer"
                >
                  <option value={1}>1 page (~10-20 results)</option>
                  <option value={2}>2 pages (~20-40 results)</option>
                  <option value={3}>3 pages (~30-60 results)</option>
                  <option value={5}>5 pages (~50-100 results)</option>
                  <option value={10}>10 pages (~100-200 results)</option>
                  <option value={25}>25 pages (~250-500 results)</option>
                  <option value={50}>50 pages (~500-1000 results)</option>
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-center space-x-4 pt-4">
            <button
              type="submit"
              disabled={loading || (!useCustomQuery && searchParams.businessCategories.length === 0) || (useCustomQuery && !searchParams.customQuery) || !searchParams.location}
              className="group relative inline-flex items-center px-8 py-4 border border-transparent text-sm font-bold rounded-xl shadow-lg text-white bg-gradient-to-r from-yellow-500 to-orange-600 hover:from-yellow-600 hover:to-orange-700 focus:outline-none focus:ring-4 focus:ring-yellow-500/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
            >
              {loading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Scraping...
                </>
              ) : (
                <>
                  <svg className="-ml-1 mr-3 h-5 w-5 group-hover:scale-110 transition-transform" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  Start Scraping
                </>
              )}
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-yellow-500 to-orange-600 opacity-0 group-hover:opacity-20 transition-opacity"></div>
            </button>

            <button
              type="button"
              onClick={handleReset}
              disabled={loading}
              className="inline-flex items-center px-6 py-4 border-2 border-gray-600 text-sm font-semibold rounded-xl text-gray-300 bg-gray-700/50 hover:bg-gray-600/50 hover:border-gray-500 focus:outline-none focus:ring-4 focus:ring-gray-500/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 backdrop-blur-sm"
            >
              <svg className="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Reset
            </button>
          </div>
        </form>
      </div>

      {/* API Information */}
      <div className="bg-gradient-to-br from-yellow-900/20 to-orange-900/20 rounded-2xl p-6 border border-yellow-500/30">
        <h3 className="text-lg font-semibold text-white mb-3">
          📋 Yellow Pages Scraper Features
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="flex items-center space-x-2">
            <span className="text-yellow-400">✓</span>
            <span className="text-gray-300">AI-powered extraction</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-yellow-400">✓</span>
            <span className="text-gray-300">US & Canadian directories</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-yellow-400">✓</span>
            <span className="text-gray-300">Comprehensive business data</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-yellow-400">✓</span>
            <span className="text-gray-300">Contact information</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-yellow-400">✓</span>
            <span className="text-gray-300">Multi-page scraping</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-yellow-400">✓</span>
            <span className="text-gray-300">Rate limiting protection</span>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-900/20 border border-red-500/30 rounded-xl p-4">
          <div className="flex items-center space-x-2">
            <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-red-300 font-medium">Error</span>
          </div>
          <p className="text-red-200 mt-2">{error}</p>
        </div>
      )}

      {/* Results Display */}
      {results.length > 0 && (
        <div className="bg-gradient-to-br from-gray-800 to-gray-700 rounded-2xl p-8 border border-gray-600 shadow-2xl">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-2xl font-bold text-white">
              Search Results ({results.length})
            </h3>
            <button
              onClick={() => {
                const csv = convertToCSV(results);
                downloadCSV(csv, 'yellow-pages-results.csv');
              }}
              className="inline-flex items-center px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg transition-colors"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Export CSV
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
            {results.map((business, index) => (
              <div key={business.id || index} className="bg-gray-700/50 rounded-lg p-4 border border-gray-600">
                <h4 className="font-semibold text-white mb-2">{business.name}</h4>
                {(typeof business.address === 'string' ? business.address : business.address?.formatted_address) && (
                  <p className="text-gray-300 text-sm mb-2">
                    {typeof business.address === 'string' ? business.address : business.address?.formatted_address}
                  </p>
                )}
                {business.contact?.phone && (
                  <p className="text-yellow-300 text-sm mb-1">📞 {business.contact.phone}</p>
                )}
                {business.contact?.website && (
                  <a
                    href={business.contact.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-yellow-400 hover:text-yellow-300 text-sm underline"
                  >
                    🌐 Website
                  </a>
                )}
                {business.rating && (
                  <p className="text-orange-400 text-sm mt-2">
                    ⭐ {business.rating.average} ({business.rating.total_reviews} reviews)
                  </p>
                )}
                {business.primary_type && (
                  <p className="text-blue-300 text-sm mt-1">
                    🏢 {business.primary_type}
                  </p>
                )}
                {business.raw_data?.description && (
                  <p className="text-gray-400 text-xs mt-2 line-clamp-2">
                    {business.raw_data.description}
                  </p>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// Helper functions for CSV export
const convertToCSV = (data: any[]) => {
  if (data.length === 0) return '';

  const headers = ['Name', 'Address', 'Phone', 'Website', 'Rating', 'Category', 'Description'];
  const rows = data.map(business => [
    business.name || '',
    typeof business.address === 'string' ? business.address : business.address?.formatted_address || '',
    business.contact?.phone || '',
    business.contact?.website || '',
    business.rating?.average || '',
    business.primary_type || '',
    business.raw_data?.description || ''
  ]);

  return [headers, ...rows].map(row =>
    row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(',')
  ).join('\n');
};

const downloadCSV = (csv: string, filename: string) => {
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
