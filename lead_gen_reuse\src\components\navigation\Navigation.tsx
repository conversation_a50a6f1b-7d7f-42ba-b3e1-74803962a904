/**
 * Navigation component for switching between different scrapers
 */

import React from 'react';
import { useRouter, ROUTES, type Route } from '../router/Router';

export const Navigation: React.FC = () => {
  const { currentRoute, navigate } = useRouter();

  return (
    <nav className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-2 border border-gray-700 mb-8">
      <div className="flex space-x-2">
        {(Object.entries(ROUTES) as [Route, typeof ROUTES[Route]][]).map(([route, config]) => {
          const isActive = currentRoute === route;
          
          return (
            <button
              key={route}
              onClick={() => navigate(route)}
              className={`
                relative flex-1 py-4 px-6 rounded-xl font-semibold text-sm transition-all duration-300 transform hover:scale-105
                ${isActive
                  ? `bg-gradient-to-r ${config.color} text-white shadow-lg shadow-blue-500/25`
                  : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                }
              `}
            >
              <div className="flex items-center justify-center space-x-3">
                <span className="text-2xl">{config.icon}</span>
                <div className="text-left">
                  <div className="font-bold">{config.title}</div>
                  <div className={`text-xs ${isActive ? 'text-white/80' : 'text-gray-500'}`}>
                    {config.description}
                  </div>
                </div>
              </div>
              
              {isActive && (
                <div className={`absolute inset-0 bg-gradient-to-r ${config.color} rounded-xl opacity-20 animate-pulse`}></div>
              )}
            </button>
          );
        })}
      </div>
    </nav>
  );
};
