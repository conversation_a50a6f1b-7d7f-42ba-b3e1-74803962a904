import React, { useState, useCallback } from 'react';
import type { SearchParams } from '../../types/common';
import { DataSource } from '../../types/common';
import { TabPanel } from './TabPanel';
import { SearchBar } from '../ui/SearchBar';
import { ExportControls } from '../ui/ExportControls';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { ErrorMessage } from '../ui/ErrorMessage';
import { useBusinessSearch } from '../../hooks/useBusinessSearch';

interface DashboardState {
  activeTab: DataSource;
  globalError: string | null;
}

const initialState: DashboardState = {
  activeTab: DataSource.GOOGLE_PLACES,
  globalError: null,
};

const TAB_LABELS: Record<DataSource, string> = {
  [DataSource.GOOGLE_PLACES]: 'Google Places',
  [DataSource.OVERPASS]: 'OpenStreetMap',
  [DataSource.YELLOW_PAGES]: 'Yellow Pages'
};

export const Dashboard: React.FC = () => {
  const [state, setState] = useState<DashboardState>(initialState);
  const { 
    state: searchState, 
    searchAll, 
    getSourceData,
  } = useBusinessSearch();

  const handleTabChange = useCallback((tab: DataSource) => {
    setState(prev => ({ ...prev, activeTab: tab }));
  }, []);

  const handleSearch = useCallback(async (searchParams: SearchParams) => {
    setState(prev => ({ ...prev, globalError: null }));
    
    try {
      await searchAll(searchParams);
    } catch (error) {
      setState(prev => ({
        ...prev,
        globalError: error instanceof Error ? error.message : 'Search failed'
      }));
    }
  }, [searchAll]);

  const handleExport = useCallback((format: 'csv' | 'json' | 'excel') => {
    const sourceData = getSourceData(state.activeTab);
    if (sourceData.businesses.length === 0) {
      alert('No data to export');
      return;
    }

    // TODO: Implement actual export functionality using export module
    console.log(`Exporting ${sourceData.businesses.length} businesses as ${format}`);
  }, [getSourceData, state.activeTab]);

  const sourceData = getSourceData(state.activeTab);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-900 to-gray-800">
      {/* Modern Header with Gradient */}
      <header className="bg-gradient-to-r from-gray-800 to-gray-700 shadow-2xl border-b border-gray-600 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-8">
            <div className="flex items-center space-x-4">
              {/* Modern Logo/Icon */}
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div>
                <h1 className="text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                  Lead Generation Dashboard
                </h1>
                <p className="mt-2 text-sm text-gray-400 font-medium">
                  Search and export business data from multiple sources
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              {/* Stats Badge */}
              {sourceData.businesses.length > 0 && (
                <div className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg">
                  {sourceData.businesses.length} leads found
                </div>
              )}
              <ExportControls
                onExport={handleExport}
                disabled={sourceData.businesses.length === 0}
                count={sourceData.businesses.length}
              />
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8">
        {/* Modern Search Bar */}
        <div className="bg-gradient-to-r from-gray-800 to-gray-700 rounded-2xl shadow-2xl border border-gray-600 p-1">
          <div className="bg-gray-800 rounded-xl">
            <SearchBar
              searchParams={searchState.lastSearch || { query: '', location: '', radius: 5000, limit: 50 }}
              onSearch={handleSearch}
              loading={sourceData.loading}
            />
          </div>
        </div>

        {/* Global Error Display */}
        {state.globalError && (
          <div className="mb-6">
            <ErrorMessage message={state.globalError} />
          </div>
        )}

        {/* Modern Tab Navigation */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-2 border border-gray-700">
          <nav className="flex space-x-2" aria-label="Tabs">
            {Object.entries(TAB_LABELS).map(([key, label]) => {
              const tabKey = key as DataSource;
              const isActive = state.activeTab === tabKey;
              const tabData = getSourceData(tabKey);

              return (
                <button
                  key={tabKey}
                  onClick={() => handleTabChange(tabKey)}
                  className={`
                    relative whitespace-nowrap py-4 px-6 font-semibold text-sm flex items-center rounded-xl transition-all duration-300 transform hover:scale-105
                    ${isActive
                      ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg shadow-blue-500/25'
                      : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                    }
                  `}
                  aria-current={isActive ? 'page' : undefined}
                >
                  {label}
                  {tabData.businesses.length > 0 && (
                    <span className={`ml-3 py-1 px-2.5 rounded-full text-xs font-bold ${
                      isActive
                        ? 'bg-white/20 text-white'
                        : 'bg-blue-500 text-white'
                    }`}>
                      {tabData.businesses.length}
                    </span>
                  )}
                  {tabData.loading && (
                    <span className="ml-3">
                      <LoadingSpinner size="small" />
                    </span>
                  )}
                  {isActive && (
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl opacity-20 animate-pulse"></div>
                  )}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Modern Tab Content */}
        <div className="bg-gradient-to-br from-gray-800 to-gray-700 shadow-2xl rounded-2xl border border-gray-600 overflow-hidden">
          <TabPanel
            dataSource={state.activeTab}
            businesses={sourceData.businesses}
            loading={sourceData.loading}
            error={sourceData.error}
            searchParams={searchState.lastSearch || { query: '', location: '', radius: 5000, limit: 50 }}
          />
        </div>
      </main>
    </div>
  );
};
