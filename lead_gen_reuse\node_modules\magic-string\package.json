{"name": "magic-string", "version": "0.30.19", "type": "commonjs", "packageManager": "pnpm@10.15.1", "description": "Modify strings, generate sourcemaps", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "repository": {"type": "git", "url": "https://github.com/rich-harris/magic-string.git"}, "license": "MIT", "author": "<PERSON>", "main": "./dist/magic-string.cjs.js", "module": "./dist/magic-string.es.mjs", "sideEffects": false, "jsnext:main": "./dist/magic-string.es.mjs", "types": "./dist/magic-string.cjs.d.ts", "exports": {"./package.json": "./package.json", ".": {"import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}}, "files": ["dist/*", "index.d.ts", "README.md"], "scripts": {"build": "rollup -c", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "format": "prettier --single-quote --print-width 100 --use-tabs --write .", "lint": "eslint src test && publint", "lint:fix": "eslint src test --fix", "prepare": "npm run build", "prepublishOnly": "npm run lint && rm -rf dist && npm test", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "pretest": "npm run build", "test": "vitest run", "test:dev": "vitest", "bench": "npm run build && node benchmark/index.mjs", "watch": "rollup -cw"}, "devDependencies": {"@eslint/js": "^9.35.0", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-replace": "^6.0.2", "benchmark": "^2.1.4", "bumpp": "^10.2.3", "conventional-changelog-cli": "^5.0.0", "eslint": "^9.35.0", "prettier": "^3.6.2", "publint": "^0.3.12", "rollup": "^4.50.1", "source-map-js": "^1.2.1", "source-map-support": "^0.5.21", "vitest": "^3.2.4"}, "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.5"}}