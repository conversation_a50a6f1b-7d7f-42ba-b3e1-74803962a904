@import "tailwindcss";

/* Modern Dark Theme Base Styles */
html {
  background-color: #111827;
}

body {
  background: linear-gradient(135deg, #111827 0%, #111827 50%, #1f2937 100%);
  color: white;
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  min-height: 100vh;
  background: transparent;
}

/* Custom scrollbar for dark theme */
.scrollbar-dark::-webkit-scrollbar {
  width: 8px;
}

.scrollbar-dark::-webkit-scrollbar-track {
  background-color: #1f2937;
  border-radius: 0.5rem;
}

.scrollbar-dark::-webkit-scrollbar-thumb {
  background-color: #4b5563;
  border-radius: 0.5rem;
}

.scrollbar-dark::-webkit-scrollbar-thumb:hover {
  background-color: #6b7280;
}
