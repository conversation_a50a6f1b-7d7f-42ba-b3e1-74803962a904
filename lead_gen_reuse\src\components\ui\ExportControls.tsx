import React, { useState } from 'react';

interface ExportControlsProps {
  onExport: (format: 'csv' | 'json' | 'excel') => void;
  disabled?: boolean;
  count?: number;
}

export const ExportControls: React.FC<ExportControlsProps> = ({
  onExport,
  disabled = false,
  count = 0
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleExport = (format: 'csv' | 'json' | 'excel') => {
    onExport(format);
    setIsOpen(false);
  };

  const exportOptions = [
    {
      format: 'csv' as const,
      label: 'CSV',
      description: 'Comma-separated values for Excel/Sheets',
      icon: '📊'
    },
    {
      format: 'json' as const,
      label: 'JSON',
      description: 'JavaScript Object Notation for developers',
      icon: '🔧'
    },
    {
      format: 'excel' as const,
      label: 'Excel',
      description: 'Microsoft Excel workbook format',
      icon: '📈'
    }
  ];

  return (
    <div className="relative">
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        disabled={disabled}
        className="group relative inline-flex items-center px-6 py-3 border-2 border-gray-600 rounded-xl shadow-lg bg-gradient-to-r from-gray-700 to-gray-600 text-sm font-bold text-white hover:from-gray-600 hover:to-gray-500 hover:border-gray-500 focus:outline-none focus:ring-4 focus:ring-blue-500/50 focus:ring-offset-2 focus:ring-offset-gray-900 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-105"
      >
        <svg className="-ml-1 mr-3 h-5 w-5 group-hover:scale-110 transition-transform" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        Export Data
        {count > 0 && (
          <span className="ml-2 bg-blue-500 text-white py-1 px-2.5 rounded-full text-xs font-bold">
            {count}
          </span>
        )}
        <svg className="ml-3 -mr-1 h-4 w-4 group-hover:rotate-180 transition-transform duration-300" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
        <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity"></div>
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute right-0 z-20 mt-2 w-72 rounded-md shadow-lg bg-gray-800 ring-1 ring-gray-600 border border-gray-600">
            <div className="py-1" role="menu" aria-orientation="vertical">
              <div className="px-4 py-2 text-xs font-medium text-gray-400 uppercase tracking-wide border-b border-gray-600">
                Export Format
              </div>

              {exportOptions.map((option) => (
                <button
                  key={option.format}
                  onClick={() => handleExport(option.format)}
                  className="w-full text-left px-4 py-3 text-sm text-white hover:bg-gray-700 focus:outline-none focus:bg-gray-700 flex items-start space-x-3 transition-colors"
                  role="menuitem"
                >
                  <span className="text-lg">{option.icon}</span>
                  <div className="flex-1">
                    <div className="font-medium">{option.label}</div>
                    <div className="text-xs text-gray-400 mt-1">
                      {option.description}
                    </div>
                  </div>
                </button>
              ))}

              {disabled && (
                <div className="px-4 py-3 text-xs text-gray-400 border-t border-gray-600">
                  No data available to export. Search for businesses first.
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
};
