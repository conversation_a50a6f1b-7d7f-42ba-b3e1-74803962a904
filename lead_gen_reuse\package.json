{"name": "lead_gen_reuse", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "e2e": "playwright test", "e2e:ui": "playwright test --ui", "preview": "vite preview", "export-sample": "node scripts/export-sample.js"}, "dependencies": {"@tailwindcss/forms": "^0.5.10", "@types/puppeteer": "^5.4.7", "axios": "^1.11.0", "cheerio": "^1.1.2", "openai": "^5.20.1", "papaparse": "^5.5.3", "puppeteer": "^24.20.0", "react": "^19.1.1", "react-dom": "^19.1.1", "tailwindcss": "^4.1.13", "zod": "^4.1.5"}, "devDependencies": {"@eslint/js": "^9.33.0", "@playwright/test": "^1.55.0", "@tailwindcss/postcss": "^4.1.13", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/cheerio": "^0.22.35", "@types/jest": "^30.0.0", "@types/node": "^24.3.1", "@types/papaparse": "^5.3.16", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@typescript-eslint/eslint-plugin": "^8.43.0", "@typescript-eslint/parser": "^8.43.0", "@vitejs/plugin-react": "^5.0.0", "autoprefixer": "^10.4.21", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "identity-obj-proxy": "^3.0.0", "jest": "^30.1.3", "jest-environment-jsdom": "^30.1.2", "msw": "^2.11.1", "playwright": "^1.55.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "ts-jest": "^29.4.1", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}}