#!/usr/bin/env python3
"""
Simple FastAPI server for Yellow Pages scraping
This is a minimal version that should work with basic dependencies
"""

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional
import asyncio
import json

app = FastAPI(title="Yellow Pages Scraper API", version="1.0.0")

# Enable CORS for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:3000", "http://127.0.0.1:5173"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

class ScrapeRequest(BaseModel):
    category: str
    location: str
    max_pages: Optional[int] = 3
    region: Optional[str] = "us"

class BusinessResponse(BaseModel):
    name: str
    address: Optional[str] = None
    phone: Optional[str] = None
    website: Optional[str] = None
    description: Optional[str] = None
    categories: Optional[List[str]] = None

class ScrapeResponse(BaseModel):
    businesses: List[BusinessResponse]
    total_found: int
    search_query: str
    location: str
    scraped_at: str

@app.get("/")
async def root():
    return {"message": "Yellow Pages Scraper API is running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "yellow-pages-scraper"}

@app.options("/scrape")
async def scrape_options():
    return {"message": "OK"}

@app.post("/scrape", response_model=ScrapeResponse)
async def scrape_businesses(request: ScrapeRequest):
    """
    Mock scrape businesses from Yellow Pages
    This is a placeholder until the full scraper is working
    """
    try:
        # Simulate processing time
        await asyncio.sleep(2)
        
        # Mock data for testing
        mock_businesses = [
            BusinessResponse(
                name=f"Sample {request.category} Business 1",
                address="123 Main St, " + request.location,
                phone="(*************",
                website="https://example1.com",
                description=f"A great {request.category} business in {request.location}",
                categories=[request.category]
            ),
            BusinessResponse(
                name=f"Sample {request.category} Business 2", 
                address="456 Oak Ave, " + request.location,
                phone="(*************",
                website="https://example2.com",
                description=f"Another excellent {request.category} service",
                categories=[request.category]
            ),
            BusinessResponse(
                name=f"Premium {request.category} Co.",
                address="789 Pine Rd, " + request.location,
                phone="(*************",
                website="https://premium-example.com",
                description=f"Premium {request.category} services since 1995",
                categories=[request.category, "premium"]
            )
        ]
        
        return ScrapeResponse(
            businesses=mock_businesses,
            total_found=len(mock_businesses),
            search_query=request.category,
            location=request.location,
            scraped_at="2025-01-16T20:00:00Z"
        )
        
    except Exception as e:
        print(f"Scraping error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Scraping failed: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    print("Starting Simple Yellow Pages Scraper API server...")
    print("Frontend can access at: http://localhost:8003")
    print("API docs available at: http://localhost:8003/docs")
    uvicorn.run(app, host="0.0.0.0", port=8003)
