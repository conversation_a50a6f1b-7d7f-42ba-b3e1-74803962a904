// @flow

/**
 * This file was automatically generated by json-schema-to-typescript.
 * DO NOT MODIFY IT BY HAND. Instead, modify the source JSONSchema file,
 * and run json-schema-to-typescript to regenerate this file.
 */
export type String = string;
/**
 * A CSS rule.
 */
export type Rule<D = Declaration, M = MediaQuery> =
  | {|
      type: "media",
      value: MediaRule<D, M>,
    |}
  | {|
      type: "import",
      value: ImportRule<M>,
    |}
  | {|
      type: "style",
      value: StyleRule<D, M>,
    |}
  | {|
      type: "keyframes",
      value: KeyframesRule<D>,
    |}
  | {|
      type: "font-face",
      value: FontFaceRule,
    |}
  | {|
      type: "font-palette-values",
      value: FontPaletteValuesRule,
    |}
  | {|
      type: "font-feature-values",
      value: FontFeatureValuesRule,
    |}
  | {|
      type: "page",
      value: PageRule<D>,
    |}
  | {|
      type: "supports",
      value: SupportsRule<D, M>,
    |}
  | {|
      type: "counter-style",
      value: CounterStyleRule<D>,
    |}
  | {|
      type: "namespace",
      value: NamespaceRule,
    |}
  | {|
      type: "moz-document",
      value: MozDocumentRule<D, M>,
    |}
  | {|
      type: "nesting",
      value: NestingRule<D, M>,
    |}
  | {|
      type: "nested-declarations",
      value: NestedDeclarationsRule<D>,
    |}
  | {|
      type: "viewport",
      value: ViewportRule<D>,
    |}
  | {|
      type: "custom-media",
      value: CustomMediaRule<M>,
    |}
  | {|
      type: "layer-statement",
      value: LayerStatementRule,
    |}
  | {|
      type: "layer-block",
      value: LayerBlockRule<D, M>,
    |}
  | {|
      type: "property",
      value: PropertyRule,
    |}
  | {|
      type: "container",
      value: ContainerRule<D, M>,
    |}
  | {|
      type: "scope",
      value: ScopeRule<D, M>,
    |}
  | {|
      type: "starting-style",
      value: StartingStyleRule<D, M>,
    |}
  | {|
      type: "view-transition",
      value: ViewTransitionRule,
    |}
  | {|
      type: "ignored",
    |}
  | {|
      type: "unknown",
      value: UnknownAtRule,
    |}
  | {|
      type: "custom",
      value: DefaultAtRule,
    |};
/**
 * Represents a media condition.
 */
export type MediaCondition =
  | {|
      type: "feature",
      value: QueryFeatureFor_MediaFeatureId,
    |}
  | {|
      type: "not",
      value: MediaCondition,
    |}
  | {|
      /**
       * The conditions for the operator.
       */
      conditions: MediaCondition[],

      /**
       * The operator for the conditions.
       */
      operator: Operator,
      type: "operation",
    |};
/**
 * A generic media feature or container feature.
 */
export type QueryFeatureFor_MediaFeatureId =
  | {|
      /**
       * The name of the feature.
       */
      name: MediaFeatureNameFor_MediaFeatureId,
      type: "plain",

      /**
       * The feature value.
       */
      value: MediaFeatureValue,
    |}
  | {|
      /**
       * The name of the feature.
       */
      name: MediaFeatureNameFor_MediaFeatureId,
      type: "boolean",
    |}
  | {|
      /**
       * The name of the feature.
       */
      name: MediaFeatureNameFor_MediaFeatureId,

      /**
       * A comparator.
       */
      operator: MediaFeatureComparison,
      type: "range",

      /**
       * The feature value.
       */
      value: MediaFeatureValue,
    |}
  | {|
      /**
       * The end value.
       */
      end: MediaFeatureValue,

      /**
       * A comparator for the end value.
       */
      endOperator: MediaFeatureComparison,

      /**
       * The name of the feature.
       */
      name: MediaFeatureNameFor_MediaFeatureId,

      /**
       * A start value.
       */
      start: MediaFeatureValue,

      /**
       * A comparator for the start value.
       */
      startOperator: MediaFeatureComparison,
      type: "interval",
    |};
/**
 * A media feature name.
 */
export type MediaFeatureNameFor_MediaFeatureId =
  | MediaFeatureId
  | String
  | String;
/**
 * A media query feature identifier.
 */
export type MediaFeatureId =
  | "width"
  | "height"
  | "aspect-ratio"
  | "orientation"
  | "overflow-block"
  | "overflow-inline"
  | "horizontal-viewport-segments"
  | "vertical-viewport-segments"
  | "display-mode"
  | "resolution"
  | "scan"
  | "grid"
  | "update"
  | "environment-blending"
  | "color"
  | "color-index"
  | "monochrome"
  | "color-gamut"
  | "dynamic-range"
  | "inverted-colors"
  | "pointer"
  | "hover"
  | "any-pointer"
  | "any-hover"
  | "nav-controls"
  | "video-color-gamut"
  | "video-dynamic-range"
  | "scripting"
  | "prefers-reduced-motion"
  | "prefers-reduced-transparency"
  | "prefers-contrast"
  | "forced-colors"
  | "prefers-color-scheme"
  | "prefers-reduced-data"
  | "device-width"
  | "device-height"
  | "device-aspect-ratio"
  | "-webkit-device-pixel-ratio"
  | "-moz-device-pixel-ratio";
/**
 * [media feature value](https://drafts.csswg.org/mediaqueries/#typedef-mf-value) within a media query.
 *
 * See [MediaFeature](MediaFeature).
 */
export type MediaFeatureValue =
  | {|
      type: "length",
      value: Length,
    |}
  | {|
      type: "number",
      value: number,
    |}
  | {|
      type: "integer",
      value: number,
    |}
  | {|
      type: "boolean",
      value: boolean,
    |}
  | {|
      type: "resolution",
      value: Resolution,
    |}
  | {|
      type: "ratio",
      value: Ratio,
    |}
  | {|
      type: "ident",
      value: String,
    |}
  | {|
      type: "env",
      value: EnvironmentVariable,
    |};
/**
 * A CSS [`<length>`](https://www.w3.org/TR/css-values-4/#lengths) value, with support for `calc()`.
 */
export type Length =
  | {|
      type: "value",
      value: LengthValue,
    |}
  | {|
      type: "calc",
      value: CalcFor_Length,
    |};
export type LengthUnit =
  | "px"
  | "in"
  | "cm"
  | "mm"
  | "q"
  | "pt"
  | "pc"
  | "em"
  | "rem"
  | "ex"
  | "rex"
  | "ch"
  | "rch"
  | "cap"
  | "rcap"
  | "ic"
  | "ric"
  | "lh"
  | "rlh"
  | "vw"
  | "lvw"
  | "svw"
  | "dvw"
  | "cqw"
  | "vh"
  | "lvh"
  | "svh"
  | "dvh"
  | "cqh"
  | "vi"
  | "svi"
  | "lvi"
  | "dvi"
  | "cqi"
  | "vb"
  | "svb"
  | "lvb"
  | "dvb"
  | "cqb"
  | "vmin"
  | "svmin"
  | "lvmin"
  | "dvmin"
  | "cqmin"
  | "vmax"
  | "svmax"
  | "lvmax"
  | "dvmax"
  | "cqmax";
/**
 * A mathematical expression used within the [`calc()`](https://www.w3.org/TR/css-values-4/#calc-func) function.
 *
 * This type supports generic value types. Values such as [Length](super::length::Length), [Percentage](super::percentage::Percentage), [Time](super::time::Time), and [Angle](super::angle::Angle) support `calc()` expressions.
 */
export type CalcFor_Length =
  | {|
      type: "value",
      value: Length,
    |}
  | {|
      type: "number",
      value: number,
    |}
  | {|
      type: "sum",

      /**
       * @minItems 2
       * @maxItems 2
       */
      value: [CalcFor_Length, CalcFor_Length],
    |}
  | {|
      type: "product",

      /**
       * @minItems 2
       * @maxItems 2
       */
      value: [number, CalcFor_Length],
    |}
  | {|
      type: "function",
      value: MathFunctionFor_Length,
    |};
/**
 * A CSS [math function](https://www.w3.org/TR/css-values-4/#math-function).
 *
 * Math functions may be used in most properties and values that accept numeric values, including lengths, percentages, angles, times, etc.
 */
export type MathFunctionFor_Length =
  | {|
      type: "calc",
      value: CalcFor_Length,
    |}
  | {|
      type: "min",
      value: CalcFor_Length[],
    |}
  | {|
      type: "max",
      value: CalcFor_Length[],
    |}
  | {|
      type: "clamp",

      /**
       * @minItems 3
       * @maxItems 3
       */
      value: [CalcFor_Length, CalcFor_Length, CalcFor_Length],
    |}
  | {|
      type: "round",

      /**
       * @minItems 3
       * @maxItems 3
       */
      value: [RoundingStrategy, CalcFor_Length, CalcFor_Length],
    |}
  | {|
      type: "rem",

      /**
       * @minItems 2
       * @maxItems 2
       */
      value: [CalcFor_Length, CalcFor_Length],
    |}
  | {|
      type: "mod",

      /**
       * @minItems 2
       * @maxItems 2
       */
      value: [CalcFor_Length, CalcFor_Length],
    |}
  | {|
      type: "abs",
      value: CalcFor_Length,
    |}
  | {|
      type: "sign",
      value: CalcFor_Length,
    |}
  | {|
      type: "hypot",
      value: CalcFor_Length[],
    |};
/**
 * A [rounding strategy](https://www.w3.org/TR/css-values-4/#typedef-rounding-strategy), as used in the `round()` function.
 */
export type RoundingStrategy = "nearest" | "up" | "down" | "to-zero";
/**
 * A CSS [`<resolution>`](https://www.w3.org/TR/css-values-4/#resolution) value.
 */
export type Resolution =
  | {|
      type: "dpi",
      value: number,
    |}
  | {|
      type: "dpcm",
      value: number,
    |}
  | {|
      type: "dppx",
      value: number,
    |};
/**
 * A CSS [`<ratio>`](https://www.w3.org/TR/css-values-4/#ratios) value, representing the ratio of two numeric values.
 * @minItems 2
 * @maxItems 2
 */
export type Ratio = [number, number];
/**
 * A raw CSS token, or a parsed value.
 */
export type TokenOrValue =
  | {|
      type: "token",
      value: Token,
    |}
  | {|
      type: "color",
      value: CssColor,
    |}
  | {|
      type: "unresolved-color",
      value: UnresolvedColor,
    |}
  | {|
      type: "url",
      value: Url,
    |}
  | {|
      type: "var",
      value: Variable,
    |}
  | {|
      type: "env",
      value: EnvironmentVariable,
    |}
  | {|
      type: "function",
      value: Function,
    |}
  | {|
      type: "length",
      value: LengthValue,
    |}
  | {|
      type: "angle",
      value: Angle,
    |}
  | {|
      type: "time",
      value: Time,
    |}
  | {|
      type: "resolution",
      value: Resolution,
    |}
  | {|
      type: "dashed-ident",
      value: String,
    |}
  | {|
      type: "animation-name",
      value: AnimationName,
    |};
/**
 * A raw CSS token.
 */
export type Token =
  | {|
      type: "ident",
      value: String,
    |}
  | {|
      type: "at-keyword",
      value: String,
    |}
  | {|
      type: "hash",
      value: String,
    |}
  | {|
      type: "id-hash",
      value: String,
    |}
  | {|
      type: "string",
      value: String,
    |}
  | {|
      type: "unquoted-url",
      value: String,
    |}
  | {|
      type: "delim",
      value: string,
    |}
  | {|
      type: "number",

      /**
       * The value as a float
       */
      value: number,
    |}
  | {|
      type: "percentage",

      /**
       * The value as a float, divided by 100 so that the nominal range is 0.0 to 1.0.
       */
      value: number,
    |}
  | {|
      type: "dimension",

      /**
       * The unit, e.g. "px" in `12px`
       */
      unit: String,

      /**
       * The value as a float
       */
      value: number,
    |}
  | {|
      type: "white-space",
      value: String,
    |}
  | {|
      type: "comment",
      value: String,
    |}
  | {|
      type: "colon",
    |}
  | {|
      type: "semicolon",
    |}
  | {|
      type: "comma",
    |}
  | {|
      type: "include-match",
    |}
  | {|
      type: "dash-match",
    |}
  | {|
      type: "prefix-match",
    |}
  | {|
      type: "suffix-match",
    |}
  | {|
      type: "substring-match",
    |}
  | {|
      type: "cdo",
    |}
  | {|
      type: "cdc",
    |}
  | {|
      type: "function",
      value: String,
    |}
  | {|
      type: "parenthesis-block",
    |}
  | {|
      type: "square-bracket-block",
    |}
  | {|
      type: "curly-bracket-block",
    |}
  | {|
      type: "bad-url",
      value: String,
    |}
  | {|
      type: "bad-string",
      value: String,
    |}
  | {|
      type: "close-parenthesis",
    |}
  | {|
      type: "close-square-bracket",
    |}
  | {|
      type: "close-curly-bracket",
    |};
/**
 * A CSS [`<color>`](https://www.w3.org/TR/css-color-4/#color-type) value.
 *
 * CSS supports many different color spaces to represent colors. The most common values are stored as RGBA using a single byte per component. Less common values are stored using a `Box` to reduce the amount of memory used per color.
 *
 * Each color space is represented as a struct that implements the `From` and `Into` traits for all other color spaces, so it is possible to convert between color spaces easily. In addition, colors support [interpolation](#method.interpolate) as in the `color-mix()` function.
 */
export type CssColor =
  | CurrentColor
  | RGBColor
  | LABColor
  | PredefinedColor
  | FloatColor
  | LightDark
  | SystemColor;
export type CurrentColor = {|
  type: "currentcolor",
|};
export type RGBColor = {|
  /**
   * The alpha component.
   */
  alpha: number,

  /**
   * The blue component.
   */
  b: number,

  /**
   * The green component.
   */
  g: number,

  /**
   * The red component.
   */
  r: number,
  type: "rgb",
|};
/**
 * A color in a LAB color space, including the `lab()`, `lch()`, `oklab()`, and `oklch()` functions.
 */
export type LABColor =
  | {|
      /**
       * The a component.
       */
      a: number,

      /**
       * The alpha component.
       */
      alpha: number,

      /**
       * The b component.
       */
      b: number,

      /**
       * The lightness component.
       */
      l: number,
      type: "lab",
    |}
  | {|
      /**
       * The alpha component.
       */
      alpha: number,

      /**
       * The chroma component.
       */
      c: number,

      /**
       * The hue component.
       */
      h: number,

      /**
       * The lightness component.
       */
      l: number,
      type: "lch",
    |}
  | {|
      /**
       * The a component.
       */
      a: number,

      /**
       * The alpha component.
       */
      alpha: number,

      /**
       * The b component.
       */
      b: number,

      /**
       * The lightness component.
       */
      l: number,
      type: "oklab",
    |}
  | {|
      /**
       * The alpha component.
       */
      alpha: number,

      /**
       * The chroma component.
       */
      c: number,

      /**
       * The hue component.
       */
      h: number,

      /**
       * The lightness component.
       */
      l: number,
      type: "oklch",
    |};
/**
 * A color in a predefined color space, e.g. `display-p3`.
 */
export type PredefinedColor =
  | {|
      /**
       * The alpha component.
       */
      alpha: number,

      /**
       * The blue component.
       */
      b: number,

      /**
       * The green component.
       */
      g: number,

      /**
       * The red component.
       */
      r: number,
      type: "srgb",
    |}
  | {|
      /**
       * The alpha component.
       */
      alpha: number,

      /**
       * The blue component.
       */
      b: number,

      /**
       * The green component.
       */
      g: number,

      /**
       * The red component.
       */
      r: number,
      type: "srgb-linear",
    |}
  | {|
      /**
       * The alpha component.
       */
      alpha: number,

      /**
       * The blue component.
       */
      b: number,

      /**
       * The green component.
       */
      g: number,

      /**
       * The red component.
       */
      r: number,
      type: "display-p3",
    |}
  | {|
      /**
       * The alpha component.
       */
      alpha: number,

      /**
       * The blue component.
       */
      b: number,

      /**
       * The green component.
       */
      g: number,

      /**
       * The red component.
       */
      r: number,
      type: "a98-rgb",
    |}
  | {|
      /**
       * The alpha component.
       */
      alpha: number,

      /**
       * The blue component.
       */
      b: number,

      /**
       * The green component.
       */
      g: number,

      /**
       * The red component.
       */
      r: number,
      type: "prophoto-rgb",
    |}
  | {|
      /**
       * The alpha component.
       */
      alpha: number,

      /**
       * The blue component.
       */
      b: number,

      /**
       * The green component.
       */
      g: number,

      /**
       * The red component.
       */
      r: number,
      type: "rec2020",
    |}
  | {|
      /**
       * The alpha component.
       */
      alpha: number,
      type: "xyz-d50",

      /**
       * The x component.
       */
      x: number,

      /**
       * The y component.
       */
      y: number,

      /**
       * The z component.
       */
      z: number,
    |}
  | {|
      /**
       * The alpha component.
       */
      alpha: number,
      type: "xyz-d65",

      /**
       * The x component.
       */
      x: number,

      /**
       * The y component.
       */
      y: number,

      /**
       * The z component.
       */
      z: number,
    |};
/**
 * A floating point representation of color types that are usually stored as RGBA. These are used when there are any `none` components, which are represented as NaN.
 */
export type FloatColor =
  | {|
      /**
       * The alpha component.
       */
      alpha: number,

      /**
       * The blue component.
       */
      b: number,

      /**
       * The green component.
       */
      g: number,

      /**
       * The red component.
       */
      r: number,
      type: "rgb",
    |}
  | {|
      /**
       * The alpha component.
       */
      alpha: number,

      /**
       * The hue component.
       */
      h: number,

      /**
       * The lightness component.
       */
      l: number,

      /**
       * The saturation component.
       */
      s: number,
      type: "hsl",
    |}
  | {|
      /**
       * The alpha component.
       */
      alpha: number,

      /**
       * The blackness component.
       */
      b: number,

      /**
       * The hue component.
       */
      h: number,
      type: "hwb",

      /**
       * The whiteness component.
       */
      w: number,
    |};
export type LightDark = {|
  dark: CssColor,
  light: CssColor,
  type: "light-dark",
|};
/**
 * A CSS [system color](https://drafts.csswg.org/css-color/#css-system-colors) keyword.
 */
export type SystemColor =
  | "accentcolor"
  | "accentcolortext"
  | "activetext"
  | "buttonborder"
  | "buttonface"
  | "buttontext"
  | "canvas"
  | "canvastext"
  | "field"
  | "fieldtext"
  | "graytext"
  | "highlight"
  | "highlighttext"
  | "linktext"
  | "mark"
  | "marktext"
  | "selecteditem"
  | "selecteditemtext"
  | "visitedtext"
  | "activeborder"
  | "activecaption"
  | "appworkspace"
  | "background"
  | "buttonhighlight"
  | "buttonshadow"
  | "captiontext"
  | "inactiveborder"
  | "inactivecaption"
  | "inactivecaptiontext"
  | "infobackground"
  | "infotext"
  | "menu"
  | "menutext"
  | "scrollbar"
  | "threeddarkshadow"
  | "threedface"
  | "threedhighlight"
  | "threedlightshadow"
  | "threedshadow"
  | "window"
  | "windowframe"
  | "windowtext";
/**
 * A color value with an unresolved alpha value (e.g. a variable). These can be converted from the modern slash syntax to older comma syntax. This can only be done when the only unresolved component is the alpha since variables can resolve to multiple tokens.
 */
export type UnresolvedColor =
  | {|
      /**
       * The unresolved alpha component.
       */
      alpha: TokenOrValue[],

      /**
       * The blue component.
       */
      b: number,

      /**
       * The green component.
       */
      g: number,

      /**
       * The red component.
       */
      r: number,
      type: "rgb",
    |}
  | {|
      /**
       * The unresolved alpha component.
       */
      alpha: TokenOrValue[],

      /**
       * The hue component.
       */
      h: number,

      /**
       * The lightness component.
       */
      l: number,

      /**
       * The saturation component.
       */
      s: number,
      type: "hsl",
    |}
  | {|
      /**
       * The dark value.
       */
      dark: TokenOrValue[],

      /**
       * The light value.
       */
      light: TokenOrValue[],
      type: "light-dark",
    |};
/**
 * Defines where the class names referenced in the `composes` property are located.
 *
 * See [Composes](Composes).
 */
export type Specifier =
  | {|
      type: "global",
    |}
  | {|
      type: "file",
      value: String,
    |}
  | {|
      type: "source-index",
      value: number,
    |};
/**
 * A CSS [`<angle>`](https://www.w3.org/TR/css-values-4/#angles) value.
 *
 * Angles may be explicit or computed by `calc()`, but are always stored and serialized as their computed value.
 */
export type Angle =
  | {|
      type: "deg",
      value: number,
    |}
  | {|
      type: "rad",
      value: number,
    |}
  | {|
      type: "grad",
      value: number,
    |}
  | {|
      type: "turn",
      value: number,
    |};
/**
 * A CSS [`<time>`](https://www.w3.org/TR/css-values-4/#time) value, in either seconds or milliseconds.
 *
 * Time values may be explicit or computed by `calc()`, but are always stored and serialized as their computed value.
 */
export type Time =
  | {|
      type: "seconds",
      value: number,
    |}
  | {|
      type: "milliseconds",
      value: number,
    |};
/**
 * A value for the [animation-name](https://drafts.csswg.org/css-animations/#animation-name) property.
 */
export type AnimationName =
  | {|
      type: "none",
    |}
  | {|
      type: "ident",
      value: String,
    |}
  | {|
      type: "string",
      value: String,
    |};
/**
 * A CSS environment variable name.
 */
export type EnvironmentVariableName =
  | {|
      type: "ua",
      value: UAEnvironmentVariable,
    |}
  | {|
      /**
       * CSS modules extension: the filename where the variable is defined. Only enabled when the CSS modules `dashed_idents` option is turned on.
       */
      from?: Specifier | null,

      /**
       * The referenced identifier.
       */
      ident: String,
      type: "custom",
    |}
  | {|
      type: "unknown",
      value: String,
    |};
/**
 * A UA-defined environment variable name.
 */
export type UAEnvironmentVariable =
  | "safe-area-inset-top"
  | "safe-area-inset-right"
  | "safe-area-inset-bottom"
  | "safe-area-inset-left"
  | "viewport-segment-width"
  | "viewport-segment-height"
  | "viewport-segment-top"
  | "viewport-segment-left"
  | "viewport-segment-bottom"
  | "viewport-segment-right";
/**
 * A [comparator](https://drafts.csswg.org/mediaqueries/#typedef-mf-comparison) within a media query.
 */
export type MediaFeatureComparison =
  | "equal"
  | "greater-than"
  | "greater-than-equal"
  | "less-than"
  | "less-than-equal";
/**
 * A binary `and` or `or` operator.
 */
export type Operator = "and" | "or";
export type MediaType = string;
/**
 * A [media query qualifier](https://drafts.csswg.org/mediaqueries/#mq-prefix).
 */
export type Qualifier = "only" | "not";
/**
 * A [`<supports-condition>`](https://drafts.csswg.org/css-conditional-3/#typedef-supports-condition), as used in the `@supports` and `@import` rules.
 */
export type SupportsCondition =
  | {|
      type: "not",
      value: SupportsCondition,
    |}
  | {|
      type: "and",
      value: SupportsCondition[],
    |}
  | {|
      type: "or",
      value: SupportsCondition[],
    |}
  | {|
      /**
       * The property id for the declaration.
       */
      propertyId: PropertyId,
      type: "declaration",

      /**
       * The raw value of the declaration.
       */
      value: String,
    |}
  | {|
      type: "selector",
      value: String,
    |}
  | {|
      type: "unknown",
      value: String,
    |};
export type PropertyId =
  | {|
      property: "background-color",
    |}
  | {|
      property: "background-image",
    |}
  | {|
      property: "background-position-x",
    |}
  | {|
      property: "background-position-y",
    |}
  | {|
      property: "background-position",
    |}
  | {|
      property: "background-size",
    |}
  | {|
      property: "background-repeat",
    |}
  | {|
      property: "background-attachment",
    |}
  | {|
      property: "background-clip",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "background-origin",
    |}
  | {|
      property: "background",
    |}
  | {|
      property: "box-shadow",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "opacity",
    |}
  | {|
      property: "color",
    |}
  | {|
      property: "display",
    |}
  | {|
      property: "visibility",
    |}
  | {|
      property: "width",
    |}
  | {|
      property: "height",
    |}
  | {|
      property: "min-width",
    |}
  | {|
      property: "min-height",
    |}
  | {|
      property: "max-width",
    |}
  | {|
      property: "max-height",
    |}
  | {|
      property: "block-size",
    |}
  | {|
      property: "inline-size",
    |}
  | {|
      property: "min-block-size",
    |}
  | {|
      property: "min-inline-size",
    |}
  | {|
      property: "max-block-size",
    |}
  | {|
      property: "max-inline-size",
    |}
  | {|
      property: "box-sizing",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "aspect-ratio",
    |}
  | {|
      property: "overflow",
    |}
  | {|
      property: "overflow-x",
    |}
  | {|
      property: "overflow-y",
    |}
  | {|
      property: "text-overflow",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "position",
    |}
  | {|
      property: "top",
    |}
  | {|
      property: "bottom",
    |}
  | {|
      property: "left",
    |}
  | {|
      property: "right",
    |}
  | {|
      property: "inset-block-start",
    |}
  | {|
      property: "inset-block-end",
    |}
  | {|
      property: "inset-inline-start",
    |}
  | {|
      property: "inset-inline-end",
    |}
  | {|
      property: "inset-block",
    |}
  | {|
      property: "inset-inline",
    |}
  | {|
      property: "inset",
    |}
  | {|
      property: "border-spacing",
    |}
  | {|
      property: "border-top-color",
    |}
  | {|
      property: "border-bottom-color",
    |}
  | {|
      property: "border-left-color",
    |}
  | {|
      property: "border-right-color",
    |}
  | {|
      property: "border-block-start-color",
    |}
  | {|
      property: "border-block-end-color",
    |}
  | {|
      property: "border-inline-start-color",
    |}
  | {|
      property: "border-inline-end-color",
    |}
  | {|
      property: "border-top-style",
    |}
  | {|
      property: "border-bottom-style",
    |}
  | {|
      property: "border-left-style",
    |}
  | {|
      property: "border-right-style",
    |}
  | {|
      property: "border-block-start-style",
    |}
  | {|
      property: "border-block-end-style",
    |}
  | {|
      property: "border-inline-start-style",
    |}
  | {|
      property: "border-inline-end-style",
    |}
  | {|
      property: "border-top-width",
    |}
  | {|
      property: "border-bottom-width",
    |}
  | {|
      property: "border-left-width",
    |}
  | {|
      property: "border-right-width",
    |}
  | {|
      property: "border-block-start-width",
    |}
  | {|
      property: "border-block-end-width",
    |}
  | {|
      property: "border-inline-start-width",
    |}
  | {|
      property: "border-inline-end-width",
    |}
  | {|
      property: "border-top-left-radius",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "border-top-right-radius",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "border-bottom-left-radius",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "border-bottom-right-radius",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "border-start-start-radius",
    |}
  | {|
      property: "border-start-end-radius",
    |}
  | {|
      property: "border-end-start-radius",
    |}
  | {|
      property: "border-end-end-radius",
    |}
  | {|
      property: "border-radius",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "border-image-source",
    |}
  | {|
      property: "border-image-outset",
    |}
  | {|
      property: "border-image-repeat",
    |}
  | {|
      property: "border-image-width",
    |}
  | {|
      property: "border-image-slice",
    |}
  | {|
      property: "border-image",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "border-color",
    |}
  | {|
      property: "border-style",
    |}
  | {|
      property: "border-width",
    |}
  | {|
      property: "border-block-color",
    |}
  | {|
      property: "border-block-style",
    |}
  | {|
      property: "border-block-width",
    |}
  | {|
      property: "border-inline-color",
    |}
  | {|
      property: "border-inline-style",
    |}
  | {|
      property: "border-inline-width",
    |}
  | {|
      property: "border",
    |}
  | {|
      property: "border-top",
    |}
  | {|
      property: "border-bottom",
    |}
  | {|
      property: "border-left",
    |}
  | {|
      property: "border-right",
    |}
  | {|
      property: "border-block",
    |}
  | {|
      property: "border-block-start",
    |}
  | {|
      property: "border-block-end",
    |}
  | {|
      property: "border-inline",
    |}
  | {|
      property: "border-inline-start",
    |}
  | {|
      property: "border-inline-end",
    |}
  | {|
      property: "outline",
    |}
  | {|
      property: "outline-color",
    |}
  | {|
      property: "outline-style",
    |}
  | {|
      property: "outline-width",
    |}
  | {|
      property: "flex-direction",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex-wrap",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex-flow",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex-grow",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex-shrink",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex-basis",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "order",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "align-content",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "justify-content",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "place-content",
    |}
  | {|
      property: "align-self",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "justify-self",
    |}
  | {|
      property: "place-self",
    |}
  | {|
      property: "align-items",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "justify-items",
    |}
  | {|
      property: "place-items",
    |}
  | {|
      property: "row-gap",
    |}
  | {|
      property: "column-gap",
    |}
  | {|
      property: "gap",
    |}
  | {|
      property: "box-orient",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "box-direction",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "box-ordinal-group",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "box-align",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "box-flex",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "box-flex-group",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "box-pack",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "box-lines",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex-pack",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex-order",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex-align",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex-item-align",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex-line-pack",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex-positive",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex-negative",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex-preferred-size",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "grid-template-columns",
    |}
  | {|
      property: "grid-template-rows",
    |}
  | {|
      property: "grid-auto-columns",
    |}
  | {|
      property: "grid-auto-rows",
    |}
  | {|
      property: "grid-auto-flow",
    |}
  | {|
      property: "grid-template-areas",
    |}
  | {|
      property: "grid-template",
    |}
  | {|
      property: "grid",
    |}
  | {|
      property: "grid-row-start",
    |}
  | {|
      property: "grid-row-end",
    |}
  | {|
      property: "grid-column-start",
    |}
  | {|
      property: "grid-column-end",
    |}
  | {|
      property: "grid-row",
    |}
  | {|
      property: "grid-column",
    |}
  | {|
      property: "grid-area",
    |}
  | {|
      property: "margin-top",
    |}
  | {|
      property: "margin-bottom",
    |}
  | {|
      property: "margin-left",
    |}
  | {|
      property: "margin-right",
    |}
  | {|
      property: "margin-block-start",
    |}
  | {|
      property: "margin-block-end",
    |}
  | {|
      property: "margin-inline-start",
    |}
  | {|
      property: "margin-inline-end",
    |}
  | {|
      property: "margin-block",
    |}
  | {|
      property: "margin-inline",
    |}
  | {|
      property: "margin",
    |}
  | {|
      property: "padding-top",
    |}
  | {|
      property: "padding-bottom",
    |}
  | {|
      property: "padding-left",
    |}
  | {|
      property: "padding-right",
    |}
  | {|
      property: "padding-block-start",
    |}
  | {|
      property: "padding-block-end",
    |}
  | {|
      property: "padding-inline-start",
    |}
  | {|
      property: "padding-inline-end",
    |}
  | {|
      property: "padding-block",
    |}
  | {|
      property: "padding-inline",
    |}
  | {|
      property: "padding",
    |}
  | {|
      property: "scroll-margin-top",
    |}
  | {|
      property: "scroll-margin-bottom",
    |}
  | {|
      property: "scroll-margin-left",
    |}
  | {|
      property: "scroll-margin-right",
    |}
  | {|
      property: "scroll-margin-block-start",
    |}
  | {|
      property: "scroll-margin-block-end",
    |}
  | {|
      property: "scroll-margin-inline-start",
    |}
  | {|
      property: "scroll-margin-inline-end",
    |}
  | {|
      property: "scroll-margin-block",
    |}
  | {|
      property: "scroll-margin-inline",
    |}
  | {|
      property: "scroll-margin",
    |}
  | {|
      property: "scroll-padding-top",
    |}
  | {|
      property: "scroll-padding-bottom",
    |}
  | {|
      property: "scroll-padding-left",
    |}
  | {|
      property: "scroll-padding-right",
    |}
  | {|
      property: "scroll-padding-block-start",
    |}
  | {|
      property: "scroll-padding-block-end",
    |}
  | {|
      property: "scroll-padding-inline-start",
    |}
  | {|
      property: "scroll-padding-inline-end",
    |}
  | {|
      property: "scroll-padding-block",
    |}
  | {|
      property: "scroll-padding-inline",
    |}
  | {|
      property: "scroll-padding",
    |}
  | {|
      property: "font-weight",
    |}
  | {|
      property: "font-size",
    |}
  | {|
      property: "font-stretch",
    |}
  | {|
      property: "font-family",
    |}
  | {|
      property: "font-style",
    |}
  | {|
      property: "font-variant-caps",
    |}
  | {|
      property: "line-height",
    |}
  | {|
      property: "font",
    |}
  | {|
      property: "vertical-align",
    |}
  | {|
      property: "font-palette",
    |}
  | {|
      property: "transition-property",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "transition-duration",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "transition-delay",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "transition-timing-function",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "transition",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "animation-name",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "animation-duration",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "animation-timing-function",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "animation-iteration-count",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "animation-direction",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "animation-play-state",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "animation-delay",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "animation-fill-mode",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "animation-composition",
    |}
  | {|
      property: "animation-timeline",
    |}
  | {|
      property: "animation-range-start",
    |}
  | {|
      property: "animation-range-end",
    |}
  | {|
      property: "animation-range",
    |}
  | {|
      property: "animation",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "transform",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "transform-origin",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "transform-style",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "transform-box",
    |}
  | {|
      property: "backface-visibility",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "perspective",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "perspective-origin",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "translate",
    |}
  | {|
      property: "rotate",
    |}
  | {|
      property: "scale",
    |}
  | {|
      property: "text-transform",
    |}
  | {|
      property: "white-space",
    |}
  | {|
      property: "tab-size",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "word-break",
    |}
  | {|
      property: "line-break",
    |}
  | {|
      property: "hyphens",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "overflow-wrap",
    |}
  | {|
      property: "word-wrap",
    |}
  | {|
      property: "text-align",
    |}
  | {|
      property: "text-align-last",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "text-justify",
    |}
  | {|
      property: "word-spacing",
    |}
  | {|
      property: "letter-spacing",
    |}
  | {|
      property: "text-indent",
    |}
  | {|
      property: "text-decoration-line",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "text-decoration-style",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "text-decoration-color",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "text-decoration-thickness",
    |}
  | {|
      property: "text-decoration",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "text-decoration-skip-ink",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "text-emphasis-style",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "text-emphasis-color",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "text-emphasis",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "text-emphasis-position",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "text-shadow",
    |}
  | {|
      property: "text-size-adjust",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "direction",
    |}
  | {|
      property: "unicode-bidi",
    |}
  | {|
      property: "box-decoration-break",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "resize",
    |}
  | {|
      property: "cursor",
    |}
  | {|
      property: "caret-color",
    |}
  | {|
      property: "caret-shape",
    |}
  | {|
      property: "caret",
    |}
  | {|
      property: "user-select",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "accent-color",
    |}
  | {|
      property: "appearance",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "list-style-type",
    |}
  | {|
      property: "list-style-image",
    |}
  | {|
      property: "list-style-position",
    |}
  | {|
      property: "list-style",
    |}
  | {|
      property: "marker-side",
    |}
  | {|
      property: "composes",
    |}
  | {|
      property: "fill",
    |}
  | {|
      property: "fill-rule",
    |}
  | {|
      property: "fill-opacity",
    |}
  | {|
      property: "stroke",
    |}
  | {|
      property: "stroke-opacity",
    |}
  | {|
      property: "stroke-width",
    |}
  | {|
      property: "stroke-linecap",
    |}
  | {|
      property: "stroke-linejoin",
    |}
  | {|
      property: "stroke-miterlimit",
    |}
  | {|
      property: "stroke-dasharray",
    |}
  | {|
      property: "stroke-dashoffset",
    |}
  | {|
      property: "marker-start",
    |}
  | {|
      property: "marker-mid",
    |}
  | {|
      property: "marker-end",
    |}
  | {|
      property: "marker",
    |}
  | {|
      property: "color-interpolation",
    |}
  | {|
      property: "color-interpolation-filters",
    |}
  | {|
      property: "color-rendering",
    |}
  | {|
      property: "shape-rendering",
    |}
  | {|
      property: "text-rendering",
    |}
  | {|
      property: "image-rendering",
    |}
  | {|
      property: "clip-path",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "clip-rule",
    |}
  | {|
      property: "mask-image",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "mask-mode",
    |}
  | {|
      property: "mask-repeat",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "mask-position-x",
    |}
  | {|
      property: "mask-position-y",
    |}
  | {|
      property: "mask-position",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "mask-clip",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "mask-origin",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "mask-size",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "mask-composite",
    |}
  | {|
      property: "mask-type",
    |}
  | {|
      property: "mask",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "mask-border-source",
    |}
  | {|
      property: "mask-border-mode",
    |}
  | {|
      property: "mask-border-slice",
    |}
  | {|
      property: "mask-border-width",
    |}
  | {|
      property: "mask-border-outset",
    |}
  | {|
      property: "mask-border-repeat",
    |}
  | {|
      property: "mask-border",
    |}
  | {|
      property: "-webkit-mask-composite",
    |}
  | {|
      property: "mask-source-type",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "mask-box-image",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "mask-box-image-source",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "mask-box-image-slice",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "mask-box-image-width",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "mask-box-image-outset",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "mask-box-image-repeat",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "filter",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "backdrop-filter",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "z-index",
    |}
  | {|
      property: "container-type",
    |}
  | {|
      property: "container-name",
    |}
  | {|
      property: "container",
    |}
  | {|
      property: "view-transition-name",
    |}
  | {|
      property: "view-transition-class",
    |}
  | {|
      property: "view-transition-group",
    |}
  | {|
      property: "color-scheme",
    |}
  | {|
      property: "all",
    |}
  | {|
      property: string,
    |};
export type Prefix = "none" | "webkit" | "moz" | "ms" | "o";
export type VendorPrefix = Prefix[];
export type Declaration =
  | {|
      property: "background-color",
      value: CssColor,
    |}
  | {|
      property: "background-image",
      value: Image[],
    |}
  | {|
      property: "background-position-x",
      value: PositionComponentFor_HorizontalPositionKeyword[],
    |}
  | {|
      property: "background-position-y",
      value: PositionComponentFor_VerticalPositionKeyword[],
    |}
  | {|
      property: "background-position",
      value: BackgroundPosition[],
    |}
  | {|
      property: "background-size",
      value: BackgroundSize[],
    |}
  | {|
      property: "background-repeat",
      value: BackgroundRepeat[],
    |}
  | {|
      property: "background-attachment",
      value: BackgroundAttachment[],
    |}
  | {|
      property: "background-clip",
      value: BackgroundClip[],
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "background-origin",
      value: BackgroundOrigin[],
    |}
  | {|
      property: "background",
      value: Background[],
    |}
  | {|
      property: "box-shadow",
      value: BoxShadow[],
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "opacity",
      value: number,
    |}
  | {|
      property: "color",
      value: CssColor,
    |}
  | {|
      property: "display",
      value: Display,
    |}
  | {|
      property: "visibility",
      value: Visibility,
    |}
  | {|
      property: "width",
      value: Size,
    |}
  | {|
      property: "height",
      value: Size,
    |}
  | {|
      property: "min-width",
      value: Size,
    |}
  | {|
      property: "min-height",
      value: Size,
    |}
  | {|
      property: "max-width",
      value: MaxSize,
    |}
  | {|
      property: "max-height",
      value: MaxSize,
    |}
  | {|
      property: "block-size",
      value: Size,
    |}
  | {|
      property: "inline-size",
      value: Size,
    |}
  | {|
      property: "min-block-size",
      value: Size,
    |}
  | {|
      property: "min-inline-size",
      value: Size,
    |}
  | {|
      property: "max-block-size",
      value: MaxSize,
    |}
  | {|
      property: "max-inline-size",
      value: MaxSize,
    |}
  | {|
      property: "box-sizing",
      value: BoxSizing,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "aspect-ratio",
      value: AspectRatio,
    |}
  | {|
      property: "overflow",
      value: Overflow,
    |}
  | {|
      property: "overflow-x",
      value: OverflowKeyword,
    |}
  | {|
      property: "overflow-y",
      value: OverflowKeyword,
    |}
  | {|
      property: "text-overflow",
      value: TextOverflow,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "position",
      value: Position2,
    |}
  | {|
      property: "top",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "bottom",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "left",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "right",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "inset-block-start",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "inset-block-end",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "inset-inline-start",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "inset-inline-end",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "inset-block",
      value: InsetBlock,
    |}
  | {|
      property: "inset-inline",
      value: InsetInline,
    |}
  | {|
      property: "inset",
      value: Inset,
    |}
  | {|
      property: "border-spacing",
      value: Size2DFor_Length,
    |}
  | {|
      property: "border-top-color",
      value: CssColor,
    |}
  | {|
      property: "border-bottom-color",
      value: CssColor,
    |}
  | {|
      property: "border-left-color",
      value: CssColor,
    |}
  | {|
      property: "border-right-color",
      value: CssColor,
    |}
  | {|
      property: "border-block-start-color",
      value: CssColor,
    |}
  | {|
      property: "border-block-end-color",
      value: CssColor,
    |}
  | {|
      property: "border-inline-start-color",
      value: CssColor,
    |}
  | {|
      property: "border-inline-end-color",
      value: CssColor,
    |}
  | {|
      property: "border-top-style",
      value: LineStyle,
    |}
  | {|
      property: "border-bottom-style",
      value: LineStyle,
    |}
  | {|
      property: "border-left-style",
      value: LineStyle,
    |}
  | {|
      property: "border-right-style",
      value: LineStyle,
    |}
  | {|
      property: "border-block-start-style",
      value: LineStyle,
    |}
  | {|
      property: "border-block-end-style",
      value: LineStyle,
    |}
  | {|
      property: "border-inline-start-style",
      value: LineStyle,
    |}
  | {|
      property: "border-inline-end-style",
      value: LineStyle,
    |}
  | {|
      property: "border-top-width",
      value: BorderSideWidth,
    |}
  | {|
      property: "border-bottom-width",
      value: BorderSideWidth,
    |}
  | {|
      property: "border-left-width",
      value: BorderSideWidth,
    |}
  | {|
      property: "border-right-width",
      value: BorderSideWidth,
    |}
  | {|
      property: "border-block-start-width",
      value: BorderSideWidth,
    |}
  | {|
      property: "border-block-end-width",
      value: BorderSideWidth,
    |}
  | {|
      property: "border-inline-start-width",
      value: BorderSideWidth,
    |}
  | {|
      property: "border-inline-end-width",
      value: BorderSideWidth,
    |}
  | {|
      property: "border-top-left-radius",
      value: Size2DFor_DimensionPercentageFor_LengthValue,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "border-top-right-radius",
      value: Size2DFor_DimensionPercentageFor_LengthValue,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "border-bottom-left-radius",
      value: Size2DFor_DimensionPercentageFor_LengthValue,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "border-bottom-right-radius",
      value: Size2DFor_DimensionPercentageFor_LengthValue,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "border-start-start-radius",
      value: Size2DFor_DimensionPercentageFor_LengthValue,
    |}
  | {|
      property: "border-start-end-radius",
      value: Size2DFor_DimensionPercentageFor_LengthValue,
    |}
  | {|
      property: "border-end-start-radius",
      value: Size2DFor_DimensionPercentageFor_LengthValue,
    |}
  | {|
      property: "border-end-end-radius",
      value: Size2DFor_DimensionPercentageFor_LengthValue,
    |}
  | {|
      property: "border-radius",
      value: BorderRadius,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "border-image-source",
      value: Image,
    |}
  | {|
      property: "border-image-outset",
      value: RectFor_LengthOrNumber,
    |}
  | {|
      property: "border-image-repeat",
      value: BorderImageRepeat,
    |}
  | {|
      property: "border-image-width",
      value: RectFor_BorderImageSideWidth,
    |}
  | {|
      property: "border-image-slice",
      value: BorderImageSlice,
    |}
  | {|
      property: "border-image",
      value: BorderImage,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "border-color",
      value: BorderColor,
    |}
  | {|
      property: "border-style",
      value: BorderStyle,
    |}
  | {|
      property: "border-width",
      value: BorderWidth,
    |}
  | {|
      property: "border-block-color",
      value: BorderBlockColor,
    |}
  | {|
      property: "border-block-style",
      value: BorderBlockStyle,
    |}
  | {|
      property: "border-block-width",
      value: BorderBlockWidth,
    |}
  | {|
      property: "border-inline-color",
      value: BorderInlineColor,
    |}
  | {|
      property: "border-inline-style",
      value: BorderInlineStyle,
    |}
  | {|
      property: "border-inline-width",
      value: BorderInlineWidth,
    |}
  | {|
      property: "border",
      value: GenericBorderFor_LineStyle,
    |}
  | {|
      property: "border-top",
      value: GenericBorderFor_LineStyle,
    |}
  | {|
      property: "border-bottom",
      value: GenericBorderFor_LineStyle,
    |}
  | {|
      property: "border-left",
      value: GenericBorderFor_LineStyle,
    |}
  | {|
      property: "border-right",
      value: GenericBorderFor_LineStyle,
    |}
  | {|
      property: "border-block",
      value: GenericBorderFor_LineStyle,
    |}
  | {|
      property: "border-block-start",
      value: GenericBorderFor_LineStyle,
    |}
  | {|
      property: "border-block-end",
      value: GenericBorderFor_LineStyle,
    |}
  | {|
      property: "border-inline",
      value: GenericBorderFor_LineStyle,
    |}
  | {|
      property: "border-inline-start",
      value: GenericBorderFor_LineStyle,
    |}
  | {|
      property: "border-inline-end",
      value: GenericBorderFor_LineStyle,
    |}
  | {|
      property: "outline",
      value: GenericBorderFor_OutlineStyleAnd_11,
    |}
  | {|
      property: "outline-color",
      value: CssColor,
    |}
  | {|
      property: "outline-style",
      value: OutlineStyle,
    |}
  | {|
      property: "outline-width",
      value: BorderSideWidth,
    |}
  | {|
      property: "flex-direction",
      value: FlexDirection,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex-wrap",
      value: FlexWrap,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex-flow",
      value: FlexFlow,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex-grow",
      value: number,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex-shrink",
      value: number,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex-basis",
      value: LengthPercentageOrAuto,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex",
      value: Flex,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "order",
      value: number,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "align-content",
      value: AlignContent,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "justify-content",
      value: JustifyContent,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "place-content",
      value: PlaceContent,
    |}
  | {|
      property: "align-self",
      value: AlignSelf,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "justify-self",
      value: JustifySelf,
    |}
  | {|
      property: "place-self",
      value: PlaceSelf,
    |}
  | {|
      property: "align-items",
      value: AlignItems,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "justify-items",
      value: JustifyItems,
    |}
  | {|
      property: "place-items",
      value: PlaceItems,
    |}
  | {|
      property: "row-gap",
      value: GapValue,
    |}
  | {|
      property: "column-gap",
      value: GapValue,
    |}
  | {|
      property: "gap",
      value: Gap,
    |}
  | {|
      property: "box-orient",
      value: BoxOrient,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "box-direction",
      value: BoxDirection,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "box-ordinal-group",
      value: number,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "box-align",
      value: BoxAlign,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "box-flex",
      value: number,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "box-flex-group",
      value: number,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "box-pack",
      value: BoxPack,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "box-lines",
      value: BoxLines,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex-pack",
      value: FlexPack,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex-order",
      value: number,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex-align",
      value: BoxAlign,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex-item-align",
      value: FlexItemAlign,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex-line-pack",
      value: FlexLinePack,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex-positive",
      value: number,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex-negative",
      value: number,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "flex-preferred-size",
      value: LengthPercentageOrAuto,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "grid-template-columns",
      value: TrackSizing,
    |}
  | {|
      property: "grid-template-rows",
      value: TrackSizing,
    |}
  | {|
      property: "grid-auto-columns",
      value: TrackSize[],
    |}
  | {|
      property: "grid-auto-rows",
      value: TrackSize[],
    |}
  | {|
      property: "grid-auto-flow",
      value: GridAutoFlow,
    |}
  | {|
      property: "grid-template-areas",
      value: GridTemplateAreas,
    |}
  | {|
      property: "grid-template",
      value: GridTemplate,
    |}
  | {|
      property: "grid",
      value: Grid,
    |}
  | {|
      property: "grid-row-start",
      value: GridLine,
    |}
  | {|
      property: "grid-row-end",
      value: GridLine,
    |}
  | {|
      property: "grid-column-start",
      value: GridLine,
    |}
  | {|
      property: "grid-column-end",
      value: GridLine,
    |}
  | {|
      property: "grid-row",
      value: GridRow,
    |}
  | {|
      property: "grid-column",
      value: GridColumn,
    |}
  | {|
      property: "grid-area",
      value: GridArea,
    |}
  | {|
      property: "margin-top",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "margin-bottom",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "margin-left",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "margin-right",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "margin-block-start",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "margin-block-end",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "margin-inline-start",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "margin-inline-end",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "margin-block",
      value: MarginBlock,
    |}
  | {|
      property: "margin-inline",
      value: MarginInline,
    |}
  | {|
      property: "margin",
      value: Margin,
    |}
  | {|
      property: "padding-top",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "padding-bottom",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "padding-left",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "padding-right",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "padding-block-start",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "padding-block-end",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "padding-inline-start",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "padding-inline-end",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "padding-block",
      value: PaddingBlock,
    |}
  | {|
      property: "padding-inline",
      value: PaddingInline,
    |}
  | {|
      property: "padding",
      value: Padding,
    |}
  | {|
      property: "scroll-margin-top",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "scroll-margin-bottom",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "scroll-margin-left",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "scroll-margin-right",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "scroll-margin-block-start",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "scroll-margin-block-end",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "scroll-margin-inline-start",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "scroll-margin-inline-end",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "scroll-margin-block",
      value: ScrollMarginBlock,
    |}
  | {|
      property: "scroll-margin-inline",
      value: ScrollMarginInline,
    |}
  | {|
      property: "scroll-margin",
      value: ScrollMargin,
    |}
  | {|
      property: "scroll-padding-top",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "scroll-padding-bottom",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "scroll-padding-left",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "scroll-padding-right",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "scroll-padding-block-start",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "scroll-padding-block-end",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "scroll-padding-inline-start",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "scroll-padding-inline-end",
      value: LengthPercentageOrAuto,
    |}
  | {|
      property: "scroll-padding-block",
      value: ScrollPaddingBlock,
    |}
  | {|
      property: "scroll-padding-inline",
      value: ScrollPaddingInline,
    |}
  | {|
      property: "scroll-padding",
      value: ScrollPadding,
    |}
  | {|
      property: "font-weight",
      value: FontWeight,
    |}
  | {|
      property: "font-size",
      value: FontSize,
    |}
  | {|
      property: "font-stretch",
      value: FontStretch,
    |}
  | {|
      property: "font-family",
      value: FontFamily[],
    |}
  | {|
      property: "font-style",
      value: FontStyle,
    |}
  | {|
      property: "font-variant-caps",
      value: FontVariantCaps,
    |}
  | {|
      property: "line-height",
      value: LineHeight,
    |}
  | {|
      property: "font",
      value: Font,
    |}
  | {|
      property: "vertical-align",
      value: VerticalAlign,
    |}
  | {|
      property: "font-palette",
      value: DashedIdentReference,
    |}
  | {|
      property: "transition-property",
      value: PropertyId[],
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "transition-duration",
      value: Time[],
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "transition-delay",
      value: Time[],
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "transition-timing-function",
      value: EasingFunction[],
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "transition",
      value: Transition[],
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "animation-name",
      value: AnimationName[],
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "animation-duration",
      value: Time[],
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "animation-timing-function",
      value: EasingFunction[],
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "animation-iteration-count",
      value: AnimationIterationCount[],
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "animation-direction",
      value: AnimationDirection[],
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "animation-play-state",
      value: AnimationPlayState[],
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "animation-delay",
      value: Time[],
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "animation-fill-mode",
      value: AnimationFillMode[],
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "animation-composition",
      value: AnimationComposition[],
    |}
  | {|
      property: "animation-timeline",
      value: AnimationTimeline[],
    |}
  | {|
      property: "animation-range-start",
      value: AnimationRangeStart[],
    |}
  | {|
      property: "animation-range-end",
      value: AnimationRangeEnd[],
    |}
  | {|
      property: "animation-range",
      value: AnimationRange[],
    |}
  | {|
      property: "animation",
      value: Animation[],
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "transform",
      value: Transform[],
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "transform-origin",
      value: Position,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "transform-style",
      value: TransformStyle,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "transform-box",
      value: TransformBox,
    |}
  | {|
      property: "backface-visibility",
      value: BackfaceVisibility,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "perspective",
      value: Perspective,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "perspective-origin",
      value: Position,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "translate",
      value: Translate,
    |}
  | {|
      property: "rotate",
      value: Rotate,
    |}
  | {|
      property: "scale",
      value: Scale,
    |}
  | {|
      property: "text-transform",
      value: TextTransform,
    |}
  | {|
      property: "white-space",
      value: WhiteSpace,
    |}
  | {|
      property: "tab-size",
      value: LengthOrNumber,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "word-break",
      value: WordBreak,
    |}
  | {|
      property: "line-break",
      value: LineBreak,
    |}
  | {|
      property: "hyphens",
      value: Hyphens,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "overflow-wrap",
      value: OverflowWrap,
    |}
  | {|
      property: "word-wrap",
      value: OverflowWrap,
    |}
  | {|
      property: "text-align",
      value: TextAlign,
    |}
  | {|
      property: "text-align-last",
      value: TextAlignLast,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "text-justify",
      value: TextJustify,
    |}
  | {|
      property: "word-spacing",
      value: Spacing,
    |}
  | {|
      property: "letter-spacing",
      value: Spacing,
    |}
  | {|
      property: "text-indent",
      value: TextIndent,
    |}
  | {|
      property: "text-decoration-line",
      value: TextDecorationLine,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "text-decoration-style",
      value: TextDecorationStyle,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "text-decoration-color",
      value: CssColor,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "text-decoration-thickness",
      value: TextDecorationThickness,
    |}
  | {|
      property: "text-decoration",
      value: TextDecoration,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "text-decoration-skip-ink",
      value: TextDecorationSkipInk,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "text-emphasis-style",
      value: TextEmphasisStyle,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "text-emphasis-color",
      value: CssColor,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "text-emphasis",
      value: TextEmphasis,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "text-emphasis-position",
      value: TextEmphasisPosition,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "text-shadow",
      value: TextShadow[],
    |}
  | {|
      property: "text-size-adjust",
      value: TextSizeAdjust,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "direction",
      value: Direction2,
    |}
  | {|
      property: "unicode-bidi",
      value: UnicodeBidi,
    |}
  | {|
      property: "box-decoration-break",
      value: BoxDecorationBreak,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "resize",
      value: Resize,
    |}
  | {|
      property: "cursor",
      value: Cursor,
    |}
  | {|
      property: "caret-color",
      value: ColorOrAuto,
    |}
  | {|
      property: "caret-shape",
      value: CaretShape,
    |}
  | {|
      property: "caret",
      value: Caret,
    |}
  | {|
      property: "user-select",
      value: UserSelect,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "accent-color",
      value: ColorOrAuto,
    |}
  | {|
      property: "appearance",
      value: Appearance,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "list-style-type",
      value: ListStyleType,
    |}
  | {|
      property: "list-style-image",
      value: Image,
    |}
  | {|
      property: "list-style-position",
      value: ListStylePosition,
    |}
  | {|
      property: "list-style",
      value: ListStyle,
    |}
  | {|
      property: "marker-side",
      value: MarkerSide,
    |}
  | {|
      property: "composes",
      value: Composes,
    |}
  | {|
      property: "fill",
      value: SVGPaint,
    |}
  | {|
      property: "fill-rule",
      value: FillRule,
    |}
  | {|
      property: "fill-opacity",
      value: number,
    |}
  | {|
      property: "stroke",
      value: SVGPaint,
    |}
  | {|
      property: "stroke-opacity",
      value: number,
    |}
  | {|
      property: "stroke-width",
      value: DimensionPercentageFor_LengthValue,
    |}
  | {|
      property: "stroke-linecap",
      value: StrokeLinecap,
    |}
  | {|
      property: "stroke-linejoin",
      value: StrokeLinejoin,
    |}
  | {|
      property: "stroke-miterlimit",
      value: number,
    |}
  | {|
      property: "stroke-dasharray",
      value: StrokeDasharray,
    |}
  | {|
      property: "stroke-dashoffset",
      value: DimensionPercentageFor_LengthValue,
    |}
  | {|
      property: "marker-start",
      value: Marker,
    |}
  | {|
      property: "marker-mid",
      value: Marker,
    |}
  | {|
      property: "marker-end",
      value: Marker,
    |}
  | {|
      property: "marker",
      value: Marker,
    |}
  | {|
      property: "color-interpolation",
      value: ColorInterpolation,
    |}
  | {|
      property: "color-interpolation-filters",
      value: ColorInterpolation,
    |}
  | {|
      property: "color-rendering",
      value: ColorRendering,
    |}
  | {|
      property: "shape-rendering",
      value: ShapeRendering,
    |}
  | {|
      property: "text-rendering",
      value: TextRendering,
    |}
  | {|
      property: "image-rendering",
      value: ImageRendering,
    |}
  | {|
      property: "clip-path",
      value: ClipPath,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "clip-rule",
      value: FillRule,
    |}
  | {|
      property: "mask-image",
      value: Image[],
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "mask-mode",
      value: MaskMode[],
    |}
  | {|
      property: "mask-repeat",
      value: BackgroundRepeat[],
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "mask-position-x",
      value: PositionComponentFor_HorizontalPositionKeyword[],
    |}
  | {|
      property: "mask-position-y",
      value: PositionComponentFor_VerticalPositionKeyword[],
    |}
  | {|
      property: "mask-position",
      value: Position[],
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "mask-clip",
      value: MaskClip[],
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "mask-origin",
      value: GeometryBox[],
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "mask-size",
      value: BackgroundSize[],
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "mask-composite",
      value: MaskComposite[],
    |}
  | {|
      property: "mask-type",
      value: MaskType,
    |}
  | {|
      property: "mask",
      value: Mask[],
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "mask-border-source",
      value: Image,
    |}
  | {|
      property: "mask-border-mode",
      value: MaskBorderMode,
    |}
  | {|
      property: "mask-border-slice",
      value: BorderImageSlice,
    |}
  | {|
      property: "mask-border-width",
      value: RectFor_BorderImageSideWidth,
    |}
  | {|
      property: "mask-border-outset",
      value: RectFor_LengthOrNumber,
    |}
  | {|
      property: "mask-border-repeat",
      value: BorderImageRepeat,
    |}
  | {|
      property: "mask-border",
      value: MaskBorder,
    |}
  | {|
      property: "-webkit-mask-composite",
      value: WebKitMaskComposite[],
    |}
  | {|
      property: "mask-source-type",
      value: WebKitMaskSourceType[],
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "mask-box-image",
      value: BorderImage,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "mask-box-image-source",
      value: Image,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "mask-box-image-slice",
      value: BorderImageSlice,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "mask-box-image-width",
      value: RectFor_BorderImageSideWidth,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "mask-box-image-outset",
      value: RectFor_LengthOrNumber,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "mask-box-image-repeat",
      value: BorderImageRepeat,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "filter",
      value: FilterList,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "backdrop-filter",
      value: FilterList,
      vendorPrefix: VendorPrefix,
    |}
  | {|
      property: "z-index",
      value: ZIndex,
    |}
  | {|
      property: "container-type",
      value: ContainerType,
    |}
  | {|
      property: "container-name",
      value: ContainerNameList,
    |}
  | {|
      property: "container",
      value: Container,
    |}
  | {|
      property: "view-transition-name",
      value: ViewTransitionName,
    |}
  | {|
      property: "view-transition-class",
      value: NoneOrCustomIdentList,
    |}
  | {|
      property: "view-transition-group",
      value: ViewTransitionGroup,
    |}
  | {|
      property: "color-scheme",
      value: ColorScheme,
    |}
  | {|
      property: "all",
      value: CSSWideKeyword,
    |}
  | {|
      property: "unparsed",
      value: UnparsedProperty,
    |}
  | {|
      property: "custom",
      value: CustomProperty,
    |};
/**
 * A CSS [`<image>`](https://www.w3.org/TR/css-images-3/#image-values) value.
 */
export type Image =
  | {|
      type: "none",
    |}
  | {|
      type: "url",
      value: Url,
    |}
  | {|
      type: "gradient",
      value: Gradient,
    |}
  | {|
      type: "image-set",
      value: ImageSet,
    |};
/**
 * A CSS [`<gradient>`](https://www.w3.org/TR/css-images-3/#gradients) value.
 */
export type Gradient =
  | {|
      /**
       * The direction of the gradient.
       */
      direction: LineDirection,

      /**
       * The color stops and transition hints for the gradient.
       */
      items: GradientItemFor_DimensionPercentageFor_LengthValue[],
      type: "linear",

      /**
       * The vendor prefixes for the gradient.
       */
      vendorPrefix: VendorPrefix,
    |}
  | {|
      /**
       * The direction of the gradient.
       */
      direction: LineDirection,

      /**
       * The color stops and transition hints for the gradient.
       */
      items: GradientItemFor_DimensionPercentageFor_LengthValue[],
      type: "repeating-linear",

      /**
       * The vendor prefixes for the gradient.
       */
      vendorPrefix: VendorPrefix,
    |}
  | {|
      /**
       * The color stops and transition hints for the gradient.
       */
      items: GradientItemFor_DimensionPercentageFor_LengthValue[],

      /**
       * The position of the gradient.
       */
      position: Position,

      /**
       * The shape of the gradient.
       */
      shape: EndingShape,
      type: "radial",

      /**
       * The vendor prefixes for the gradient.
       */
      vendorPrefix: VendorPrefix,
    |}
  | {|
      /**
       * The color stops and transition hints for the gradient.
       */
      items: GradientItemFor_DimensionPercentageFor_LengthValue[],

      /**
       * The position of the gradient.
       */
      position: Position,

      /**
       * The shape of the gradient.
       */
      shape: EndingShape,
      type: "repeating-radial",

      /**
       * The vendor prefixes for the gradient.
       */
      vendorPrefix: VendorPrefix,
    |}
  | {|
      /**
       * The angle of the gradient.
       */
      angle: Angle,

      /**
       * The color stops and transition hints for the gradient.
       */
      items: GradientItemFor_DimensionPercentageFor_Angle[],

      /**
       * The position of the gradient.
       */
      position: Position,
      type: "conic",
    |}
  | {|
      /**
       * The angle of the gradient.
       */
      angle: Angle,

      /**
       * The color stops and transition hints for the gradient.
       */
      items: GradientItemFor_DimensionPercentageFor_Angle[],

      /**
       * The position of the gradient.
       */
      position: Position,
      type: "repeating-conic",
    |}
  | (
      | {|
          type: "webkit-gradient",

          /**
           * The starting point of the gradient.
           */
          from: WebKitGradientPoint,
          kind: "linear",

          /**
           * The color stops in the gradient.
           */
          stops: WebKitColorStop[],

          /**
           * The ending point of the gradient.
           */
          to: WebKitGradientPoint,
        |}
      | {|
          type: "webkit-gradient",

          /**
           * The starting point of the gradient.
           */
          from: WebKitGradientPoint,
          kind: "radial",

          /**
           * The starting radius of the gradient.
           */
          r0: number,

          /**
           * The ending radius of the gradient.
           */
          r1: number,

          /**
           * The color stops in the gradient.
           */
          stops: WebKitColorStop[],

          /**
           * The ending point of the gradient.
           */
          to: WebKitGradientPoint,
        |}
    );
/**
 * The direction of a CSS `linear-gradient()`.
 *
 * See [LinearGradient](LinearGradient).
 */
export type LineDirection =
  | {|
      type: "angle",
      value: Angle,
    |}
  | {|
      type: "horizontal",
      value: HorizontalPositionKeyword,
    |}
  | {|
      type: "vertical",
      value: VerticalPositionKeyword,
    |}
  | {|
      /**
       * A horizontal position keyword, e.g. `left` or `right.
       */
      horizontal: HorizontalPositionKeyword,
      type: "corner",

      /**
       * A vertical posision keyword, e.g. `top` or `bottom`.
       */
      vertical: VerticalPositionKeyword,
    |};
/**
 * A horizontal position keyword.
 */
export type HorizontalPositionKeyword = "left" | "right";
/**
 * A vertical position keyword.
 */
export type VerticalPositionKeyword = "top" | "bottom";
/**
 * Either a color stop or interpolation hint within a gradient.
 *
 * This type is generic, and items may be either a [LengthPercentage](super::length::LengthPercentage) or [Angle](super::angle::Angle) depending on what type of gradient it is within.
 */
export type GradientItemFor_DimensionPercentageFor_LengthValue =
  | {|
      /**
       * The color of the color stop.
       */
      color: CssColor,

      /**
       * The position of the color stop.
       */
      position?: DimensionPercentageFor_LengthValue | null,
      type: "color-stop",
    |}
  | {|
      type: "hint",
      value: DimensionPercentageFor_LengthValue,
    |};
/**
 * A generic type that allows any kind of dimension and percentage to be used standalone or mixed within a `calc()` expression.
 *
 * <https://drafts.csswg.org/css-values-4/#mixed-percentages>
 */
export type DimensionPercentageFor_LengthValue =
  | {|
      type: "dimension",
      value: LengthValue,
    |}
  | {|
      type: "percentage",
      value: number,
    |}
  | {|
      type: "calc",
      value: CalcFor_DimensionPercentageFor_LengthValue,
    |};
/**
 * A mathematical expression used within the [`calc()`](https://www.w3.org/TR/css-values-4/#calc-func) function.
 *
 * This type supports generic value types. Values such as [Length](super::length::Length), [Percentage](super::percentage::Percentage), [Time](super::time::Time), and [Angle](super::angle::Angle) support `calc()` expressions.
 */
export type CalcFor_DimensionPercentageFor_LengthValue =
  | {|
      type: "value",
      value: DimensionPercentageFor_LengthValue,
    |}
  | {|
      type: "number",
      value: number,
    |}
  | {|
      type: "sum",

      /**
       * @minItems 2
       * @maxItems 2
       */
      value: [
        CalcFor_DimensionPercentageFor_LengthValue,
        CalcFor_DimensionPercentageFor_LengthValue
      ],
    |}
  | {|
      type: "product",

      /**
       * @minItems 2
       * @maxItems 2
       */
      value: [number, CalcFor_DimensionPercentageFor_LengthValue],
    |}
  | {|
      type: "function",
      value: MathFunctionFor_DimensionPercentageFor_LengthValue,
    |};
/**
 * A CSS [math function](https://www.w3.org/TR/css-values-4/#math-function).
 *
 * Math functions may be used in most properties and values that accept numeric values, including lengths, percentages, angles, times, etc.
 */
export type MathFunctionFor_DimensionPercentageFor_LengthValue =
  | {|
      type: "calc",
      value: CalcFor_DimensionPercentageFor_LengthValue,
    |}
  | {|
      type: "min",
      value: CalcFor_DimensionPercentageFor_LengthValue[],
    |}
  | {|
      type: "max",
      value: CalcFor_DimensionPercentageFor_LengthValue[],
    |}
  | {|
      type: "clamp",

      /**
       * @minItems 3
       * @maxItems 3
       */
      value: [
        CalcFor_DimensionPercentageFor_LengthValue,
        CalcFor_DimensionPercentageFor_LengthValue,
        CalcFor_DimensionPercentageFor_LengthValue
      ],
    |}
  | {|
      type: "round",

      /**
       * @minItems 3
       * @maxItems 3
       */
      value: [
        RoundingStrategy,
        CalcFor_DimensionPercentageFor_LengthValue,
        CalcFor_DimensionPercentageFor_LengthValue
      ],
    |}
  | {|
      type: "rem",

      /**
       * @minItems 2
       * @maxItems 2
       */
      value: [
        CalcFor_DimensionPercentageFor_LengthValue,
        CalcFor_DimensionPercentageFor_LengthValue
      ],
    |}
  | {|
      type: "mod",

      /**
       * @minItems 2
       * @maxItems 2
       */
      value: [
        CalcFor_DimensionPercentageFor_LengthValue,
        CalcFor_DimensionPercentageFor_LengthValue
      ],
    |}
  | {|
      type: "abs",
      value: CalcFor_DimensionPercentageFor_LengthValue,
    |}
  | {|
      type: "sign",
      value: CalcFor_DimensionPercentageFor_LengthValue,
    |}
  | {|
      type: "hypot",
      value: CalcFor_DimensionPercentageFor_LengthValue[],
    |};
/**
 * A component within a [Position](Position) value, representing a position along either the horizontal or vertical axis of a box.
 *
 * This type is generic over side keywords.
 */
export type PositionComponentFor_HorizontalPositionKeyword =
  | {|
      type: "center",
    |}
  | {|
      type: "length",
      value: DimensionPercentageFor_LengthValue,
    |}
  | {|
      /**
       * Offset from the side.
       */
      offset?: DimensionPercentageFor_LengthValue | null,

      /**
       * A side keyword.
       */
      side: HorizontalPositionKeyword,
      type: "side",
    |};
/**
 * A component within a [Position](Position) value, representing a position along either the horizontal or vertical axis of a box.
 *
 * This type is generic over side keywords.
 */
export type PositionComponentFor_VerticalPositionKeyword =
  | {|
      type: "center",
    |}
  | {|
      type: "length",
      value: DimensionPercentageFor_LengthValue,
    |}
  | {|
      /**
       * Offset from the side.
       */
      offset?: DimensionPercentageFor_LengthValue | null,

      /**
       * A side keyword.
       */
      side: VerticalPositionKeyword,
      type: "side",
    |};
/**
 * A `radial-gradient()` [ending shape](https://www.w3.org/TR/css-images-3/#valdef-radial-gradient-ending-shape).
 *
 * See [RadialGradient](RadialGradient).
 */
export type EndingShape =
  | {|
      type: "ellipse",
      value: Ellipse,
    |}
  | {|
      type: "circle",
      value: Circle,
    |};
/**
 * An ellipse ending shape for a `radial-gradient()`.
 *
 * See [RadialGradient](RadialGradient).
 */
export type Ellipse =
  | {|
      type: "size",

      /**
       * The x-radius of the ellipse.
       */
      x: DimensionPercentageFor_LengthValue,

      /**
       * The y-radius of the ellipse.
       */
      y: DimensionPercentageFor_LengthValue,
    |}
  | {|
      type: "extent",
      value: ShapeExtent,
    |};
/**
 * A shape extent for a `radial-gradient()`.
 *
 * See [RadialGradient](RadialGradient).
 */
export type ShapeExtent =
  | "closest-side"
  | "farthest-side"
  | "closest-corner"
  | "farthest-corner";
/**
 * A circle ending shape for a `radial-gradient()`.
 *
 * See [RadialGradient](RadialGradient).
 */
export type Circle =
  | {|
      type: "radius",
      value: Length,
    |}
  | {|
      type: "extent",
      value: ShapeExtent,
    |};
/**
 * Either a color stop or interpolation hint within a gradient.
 *
 * This type is generic, and items may be either a [LengthPercentage](super::length::LengthPercentage) or [Angle](super::angle::Angle) depending on what type of gradient it is within.
 */
export type GradientItemFor_DimensionPercentageFor_Angle =
  | {|
      /**
       * The color of the color stop.
       */
      color: CssColor,

      /**
       * The position of the color stop.
       */
      position?: DimensionPercentageFor_Angle | null,
      type: "color-stop",
    |}
  | {|
      type: "hint",
      value: DimensionPercentageFor_Angle,
    |};
/**
 * A generic type that allows any kind of dimension and percentage to be used standalone or mixed within a `calc()` expression.
 *
 * <https://drafts.csswg.org/css-values-4/#mixed-percentages>
 */
export type DimensionPercentageFor_Angle =
  | {|
      type: "dimension",
      value: Angle,
    |}
  | {|
      type: "percentage",
      value: number,
    |}
  | {|
      type: "calc",
      value: CalcFor_DimensionPercentageFor_Angle,
    |};
/**
 * A mathematical expression used within the [`calc()`](https://www.w3.org/TR/css-values-4/#calc-func) function.
 *
 * This type supports generic value types. Values such as [Length](super::length::Length), [Percentage](super::percentage::Percentage), [Time](super::time::Time), and [Angle](super::angle::Angle) support `calc()` expressions.
 */
export type CalcFor_DimensionPercentageFor_Angle =
  | {|
      type: "value",
      value: DimensionPercentageFor_Angle,
    |}
  | {|
      type: "number",
      value: number,
    |}
  | {|
      type: "sum",

      /**
       * @minItems 2
       * @maxItems 2
       */
      value: [
        CalcFor_DimensionPercentageFor_Angle,
        CalcFor_DimensionPercentageFor_Angle
      ],
    |}
  | {|
      type: "product",

      /**
       * @minItems 2
       * @maxItems 2
       */
      value: [number, CalcFor_DimensionPercentageFor_Angle],
    |}
  | {|
      type: "function",
      value: MathFunctionFor_DimensionPercentageFor_Angle,
    |};
/**
 * A CSS [math function](https://www.w3.org/TR/css-values-4/#math-function).
 *
 * Math functions may be used in most properties and values that accept numeric values, including lengths, percentages, angles, times, etc.
 */
export type MathFunctionFor_DimensionPercentageFor_Angle =
  | {|
      type: "calc",
      value: CalcFor_DimensionPercentageFor_Angle,
    |}
  | {|
      type: "min",
      value: CalcFor_DimensionPercentageFor_Angle[],
    |}
  | {|
      type: "max",
      value: CalcFor_DimensionPercentageFor_Angle[],
    |}
  | {|
      type: "clamp",

      /**
       * @minItems 3
       * @maxItems 3
       */
      value: [
        CalcFor_DimensionPercentageFor_Angle,
        CalcFor_DimensionPercentageFor_Angle,
        CalcFor_DimensionPercentageFor_Angle
      ],
    |}
  | {|
      type: "round",

      /**
       * @minItems 3
       * @maxItems 3
       */
      value: [
        RoundingStrategy,
        CalcFor_DimensionPercentageFor_Angle,
        CalcFor_DimensionPercentageFor_Angle
      ],
    |}
  | {|
      type: "rem",

      /**
       * @minItems 2
       * @maxItems 2
       */
      value: [
        CalcFor_DimensionPercentageFor_Angle,
        CalcFor_DimensionPercentageFor_Angle
      ],
    |}
  | {|
      type: "mod",

      /**
       * @minItems 2
       * @maxItems 2
       */
      value: [
        CalcFor_DimensionPercentageFor_Angle,
        CalcFor_DimensionPercentageFor_Angle
      ],
    |}
  | {|
      type: "abs",
      value: CalcFor_DimensionPercentageFor_Angle,
    |}
  | {|
      type: "sign",
      value: CalcFor_DimensionPercentageFor_Angle,
    |}
  | {|
      type: "hypot",
      value: CalcFor_DimensionPercentageFor_Angle[],
    |};
/**
 * A keyword or number within a [WebKitGradientPoint](WebKitGradientPoint).
 */
export type WebKitGradientPointComponentFor_HorizontalPositionKeyword =
  | {|
      type: "center",
    |}
  | {|
      type: "number",
      value: NumberOrPercentage,
    |}
  | {|
      type: "side",
      value: HorizontalPositionKeyword,
    |};
/**
 * Either a `<number>` or `<percentage>`.
 */
export type NumberOrPercentage =
  | {|
      type: "number",
      value: number,
    |}
  | {|
      type: "percentage",
      value: number,
    |};
/**
 * A keyword or number within a [WebKitGradientPoint](WebKitGradientPoint).
 */
export type WebKitGradientPointComponentFor_VerticalPositionKeyword =
  | {|
      type: "center",
    |}
  | {|
      type: "number",
      value: NumberOrPercentage,
    |}
  | {|
      type: "side",
      value: VerticalPositionKeyword,
    |};
/**
 * A value for the [background-size](https://www.w3.org/TR/css-backgrounds-3/#background-size) property.
 */
export type BackgroundSize =
  | {|
      /**
       * The height of the background.
       */
      height: LengthPercentageOrAuto,
      type: "explicit",

      /**
       * The width of the background.
       */
      width: LengthPercentageOrAuto,
    |}
  | {|
      type: "cover",
    |}
  | {|
      type: "contain",
    |};
/**
 * Either a [`<length-percentage>`](https://www.w3.org/TR/css-values-4/#typedef-length-percentage), or the `auto` keyword.
 */
export type LengthPercentageOrAuto =
  | {|
      type: "auto",
    |}
  | {|
      type: "length-percentage",
      value: DimensionPercentageFor_LengthValue,
    |};
/**
 * A [`<repeat-style>`](https://www.w3.org/TR/css-backgrounds-3/#typedef-repeat-style) value, used within the `background-repeat` property to represent how a background image is repeated in a single direction.
 *
 * See [BackgroundRepeat](BackgroundRepeat).
 */
export type BackgroundRepeatKeyword =
  | "repeat"
  | "space"
  | "round"
  | "no-repeat";
/**
 * A value for the [background-attachment](https://www.w3.org/TR/css-backgrounds-3/#background-attachment) property.
 */
export type BackgroundAttachment = "scroll" | "fixed" | "local";
/**
 * A value for the [background-clip](https://drafts.csswg.org/css-backgrounds-4/#background-clip) property.
 */
export type BackgroundClip =
  | "border-box"
  | "padding-box"
  | "content-box"
  | "border"
  | "text";
/**
 * A value for the [background-origin](https://www.w3.org/TR/css-backgrounds-3/#background-origin) property.
 */
export type BackgroundOrigin = "border-box" | "padding-box" | "content-box";
/**
 * A value for the [display](https://drafts.csswg.org/css-display-3/#the-display-properties) property.
 */
export type Display =
  | {|
      type: "keyword",
      value: DisplayKeyword,
    |}
  | {|
      /**
       * The inside display value.
       */
      inside: DisplayInside,

      /**
       * Whether this is a list item.
       */
      isListItem: boolean,

      /**
       * The outside display value.
       */
      outside: DisplayOutside,
      type: "pair",
    |};
/**
 * A `display` keyword.
 *
 * See [Display](Display).
 */
export type DisplayKeyword =
  | "none"
  | "contents"
  | "table-row-group"
  | "table-header-group"
  | "table-footer-group"
  | "table-row"
  | "table-cell"
  | "table-column-group"
  | "table-column"
  | "table-caption"
  | "ruby-base"
  | "ruby-text"
  | "ruby-base-container"
  | "ruby-text-container";
/**
 * A [`<display-inside>`](https://drafts.csswg.org/css-display-3/#typedef-display-inside) value.
 */
export type DisplayInside =
  | {|
      type: "flow",
    |}
  | {|
      type: "flow-root",
    |}
  | {|
      type: "table",
    |}
  | {|
      type: "flex",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      type: "box",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      type: "grid",
    |}
  | {|
      type: "ruby",
    |};
/**
 * A [`<display-outside>`](https://drafts.csswg.org/css-display-3/#typedef-display-outside) value.
 */
export type DisplayOutside = "block" | "inline" | "run-in";
/**
 * A value for the [visibility](https://drafts.csswg.org/css-display-3/#visibility) property.
 */
export type Visibility = "visible" | "hidden" | "collapse";
/**
 * A value for the [preferred size properties](https://drafts.csswg.org/css-sizing-3/#preferred-size-properties), i.e. `width` and `height.
 */
export type Size =
  | {|
      type: "auto",
    |}
  | {|
      type: "length-percentage",
      value: DimensionPercentageFor_LengthValue,
    |}
  | {|
      type: "min-content",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      type: "max-content",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      type: "fit-content",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      type: "fit-content-function",
      value: DimensionPercentageFor_LengthValue,
    |}
  | {|
      type: "stretch",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      type: "contain",
    |};
/**
 * A value for the [minimum](https://drafts.csswg.org/css-sizing-3/#min-size-properties) and [maximum](https://drafts.csswg.org/css-sizing-3/#max-size-properties) size properties, e.g. `min-width` and `max-height`.
 */
export type MaxSize =
  | {|
      type: "none",
    |}
  | {|
      type: "length-percentage",
      value: DimensionPercentageFor_LengthValue,
    |}
  | {|
      type: "min-content",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      type: "max-content",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      type: "fit-content",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      type: "fit-content-function",
      value: DimensionPercentageFor_LengthValue,
    |}
  | {|
      type: "stretch",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      type: "contain",
    |};
/**
 * A value for the [box-sizing](https://drafts.csswg.org/css-sizing-3/#box-sizing) property.
 */
export type BoxSizing = "content-box" | "border-box";
/**
 * An [overflow](https://www.w3.org/TR/css-overflow-3/#overflow-properties) keyword as used in the `overflow-x`, `overflow-y`, and `overflow` properties.
 */
export type OverflowKeyword = "visible" | "hidden" | "clip" | "scroll" | "auto";
/**
 * A value for the [text-overflow](https://www.w3.org/TR/css-overflow-3/#text-overflow) property.
 */
export type TextOverflow = "clip" | "ellipsis";
/**
 * A value for the [position](https://www.w3.org/TR/css-position-3/#position-property) property.
 */
export type Position2 =
  | {|
      type: "static",
    |}
  | {|
      type: "relative",
    |}
  | {|
      type: "absolute",
    |}
  | {|
      type: "sticky",
      value: VendorPrefix,
    |}
  | {|
      type: "fixed",
    |};
/**
 * A generic value that represents a value with two components, e.g. a border radius.
 *
 * When serialized, only a single component will be written if both are equal.
 * @minItems 2
 * @maxItems 2
 */
export type Size2DFor_Length = [Length, Length];
/**
 * A [`<line-style>`](https://drafts.csswg.org/css-backgrounds/#typedef-line-style) value, used in the `border-style` property.
 */
export type LineStyle =
  | "none"
  | "hidden"
  | "inset"
  | "groove"
  | "outset"
  | "ridge"
  | "dotted"
  | "dashed"
  | "solid"
  | "double";
/**
 * A value for the [border-width](https://www.w3.org/TR/css-backgrounds-3/#border-width) property.
 */
export type BorderSideWidth =
  | {|
      type: "thin",
    |}
  | {|
      type: "medium",
    |}
  | {|
      type: "thick",
    |}
  | {|
      type: "length",
      value: Length,
    |};
/**
 * A generic value that represents a value with two components, e.g. a border radius.
 *
 * When serialized, only a single component will be written if both are equal.
 * @minItems 2
 * @maxItems 2
 */
export type Size2DFor_DimensionPercentageFor_LengthValue = [
  DimensionPercentageFor_LengthValue,
  DimensionPercentageFor_LengthValue
];
/**
 * A generic value that represents a value for four sides of a box, e.g. border-width, margin, padding, etc.
 *
 * When serialized, as few components as possible are written when there are duplicate values.
 * @minItems 4
 * @maxItems 4
 */
export type RectFor_LengthOrNumber = [
  LengthOrNumber,
  LengthOrNumber,
  LengthOrNumber,
  LengthOrNumber
];
/**
 * Either a [`<length>`](https://www.w3.org/TR/css-values-4/#lengths) or a [`<number>`](https://www.w3.org/TR/css-values-4/#numbers).
 */
export type LengthOrNumber =
  | {|
      type: "number",
      value: number,
    |}
  | {|
      type: "length",
      value: Length,
    |};
/**
 * A single [border-image-repeat](https://www.w3.org/TR/css-backgrounds-3/#border-image-repeat) keyword.
 */
export type BorderImageRepeatKeyword = "stretch" | "repeat" | "round" | "space";
/**
 * A generic value that represents a value for four sides of a box, e.g. border-width, margin, padding, etc.
 *
 * When serialized, as few components as possible are written when there are duplicate values.
 * @minItems 4
 * @maxItems 4
 */
export type RectFor_BorderImageSideWidth = [
  BorderImageSideWidth,
  BorderImageSideWidth,
  BorderImageSideWidth,
  BorderImageSideWidth
];
/**
 * A value for the [border-image-width](https://www.w3.org/TR/css-backgrounds-3/#border-image-width) property.
 */
export type BorderImageSideWidth =
  | {|
      type: "number",
      value: number,
    |}
  | {|
      type: "length-percentage",
      value: DimensionPercentageFor_LengthValue,
    |}
  | {|
      type: "auto",
    |};
/**
 * A generic value that represents a value for four sides of a box, e.g. border-width, margin, padding, etc.
 *
 * When serialized, as few components as possible are written when there are duplicate values.
 * @minItems 4
 * @maxItems 4
 */
export type RectFor_NumberOrPercentage = [
  NumberOrPercentage,
  NumberOrPercentage,
  NumberOrPercentage,
  NumberOrPercentage
];
/**
 * A value for the [outline-style](https://drafts.csswg.org/css-ui/#outline-style) property.
 */
export type OutlineStyle =
  | {|
      type: "auto",
    |}
  | {|
      type: "line-style",
      value: LineStyle,
    |};
/**
 * A value for the [flex-direction](https://www.w3.org/TR/2018/CR-css-flexbox-1-20181119/#propdef-flex-direction) property.
 */
export type FlexDirection = "row" | "row-reverse" | "column" | "column-reverse";
/**
 * A value for the [flex-wrap](https://www.w3.org/TR/2018/CR-css-flexbox-1-20181119/#flex-wrap-property) property.
 */
export type FlexWrap = "nowrap" | "wrap" | "wrap-reverse";
/**
 * A value for the [align-content](https://www.w3.org/TR/css-align-3/#propdef-align-content) property.
 */
export type AlignContent =
  | {|
      type: "normal",
    |}
  | {|
      type: "baseline-position",
      value: BaselinePosition,
    |}
  | {|
      type: "content-distribution",
      value: ContentDistribution,
    |}
  | {|
      /**
       * An overflow alignment mode.
       */
      overflow?: OverflowPosition | null,
      type: "content-position",

      /**
       * A content position keyword.
       */
      value: ContentPosition,
    |};
/**
 * A [`<baseline-position>`](https://www.w3.org/TR/css-align-3/#typedef-baseline-position) value, as used in the alignment properties.
 */
export type BaselinePosition = "first" | "last";
/**
 * A [`<content-distribution>`](https://www.w3.org/TR/css-align-3/#typedef-content-distribution) value.
 */
export type ContentDistribution =
  | "space-between"
  | "space-around"
  | "space-evenly"
  | "stretch";
/**
 * An [`<overflow-position>`](https://www.w3.org/TR/css-align-3/#typedef-overflow-position) value.
 */
export type OverflowPosition = "safe" | "unsafe";
/**
 * A [`<content-position>`](https://www.w3.org/TR/css-align-3/#typedef-content-position) value.
 */
export type ContentPosition =
  | "center"
  | "start"
  | "end"
  | "flex-start"
  | "flex-end";
/**
 * A value for the [justify-content](https://www.w3.org/TR/css-align-3/#propdef-justify-content) property.
 */
export type JustifyContent =
  | {|
      type: "normal",
    |}
  | {|
      type: "content-distribution",
      value: ContentDistribution,
    |}
  | {|
      /**
       * An overflow alignment mode.
       */
      overflow?: OverflowPosition | null,
      type: "content-position",

      /**
       * A content position keyword.
       */
      value: ContentPosition,
    |}
  | {|
      /**
       * An overflow alignment mode.
       */
      overflow?: OverflowPosition | null,
      type: "left",
    |}
  | {|
      /**
       * An overflow alignment mode.
       */
      overflow?: OverflowPosition | null,
      type: "right",
    |};
/**
 * A value for the [align-self](https://www.w3.org/TR/css-align-3/#align-self-property) property.
 */
export type AlignSelf =
  | {|
      type: "auto",
    |}
  | {|
      type: "normal",
    |}
  | {|
      type: "stretch",
    |}
  | {|
      type: "baseline-position",
      value: BaselinePosition,
    |}
  | {|
      /**
       * An overflow alignment mode.
       */
      overflow?: OverflowPosition | null,
      type: "self-position",

      /**
       * A self position keyword.
       */
      value: SelfPosition,
    |};
/**
 * A [`<self-position>`](https://www.w3.org/TR/css-align-3/#typedef-self-position) value.
 */
export type SelfPosition =
  | "center"
  | "start"
  | "end"
  | "self-start"
  | "self-end"
  | "flex-start"
  | "flex-end";
/**
 * A value for the [justify-self](https://www.w3.org/TR/css-align-3/#justify-self-property) property.
 */
export type JustifySelf =
  | {|
      type: "auto",
    |}
  | {|
      type: "normal",
    |}
  | {|
      type: "stretch",
    |}
  | {|
      type: "baseline-position",
      value: BaselinePosition,
    |}
  | {|
      /**
       * An overflow alignment mode.
       */
      overflow?: OverflowPosition | null,
      type: "self-position",

      /**
       * A self position keyword.
       */
      value: SelfPosition,
    |}
  | {|
      /**
       * An overflow alignment mode.
       */
      overflow?: OverflowPosition | null,
      type: "left",
    |}
  | {|
      /**
       * An overflow alignment mode.
       */
      overflow?: OverflowPosition | null,
      type: "right",
    |};
/**
 * A value for the [align-items](https://www.w3.org/TR/css-align-3/#align-items-property) property.
 */
export type AlignItems =
  | {|
      type: "normal",
    |}
  | {|
      type: "stretch",
    |}
  | {|
      type: "baseline-position",
      value: BaselinePosition,
    |}
  | {|
      /**
       * An overflow alignment mode.
       */
      overflow?: OverflowPosition | null,
      type: "self-position",

      /**
       * A self position keyword.
       */
      value: SelfPosition,
    |};
/**
 * A value for the [justify-items](https://www.w3.org/TR/css-align-3/#justify-items-property) property.
 */
export type JustifyItems =
  | {|
      type: "normal",
    |}
  | {|
      type: "stretch",
    |}
  | {|
      type: "baseline-position",
      value: BaselinePosition,
    |}
  | {|
      /**
       * An overflow alignment mode.
       */
      overflow?: OverflowPosition | null,
      type: "self-position",

      /**
       * A self position keyword.
       */
      value: SelfPosition,
    |}
  | {|
      /**
       * An overflow alignment mode.
       */
      overflow?: OverflowPosition | null,
      type: "left",
    |}
  | {|
      /**
       * An overflow alignment mode.
       */
      overflow?: OverflowPosition | null,
      type: "right",
    |}
  | {|
      type: "legacy",
      value: LegacyJustify,
    |};
/**
 * A legacy justification keyword, as used in the `justify-items` property.
 */
export type LegacyJustify = "left" | "right" | "center";
/**
 * A [gap](https://www.w3.org/TR/css-align-3/#column-row-gap) value, as used in the `column-gap` and `row-gap` properties.
 */
export type GapValue =
  | {|
      type: "normal",
    |}
  | {|
      type: "length-percentage",
      value: DimensionPercentageFor_LengthValue,
    |};
/**
 * A value for the legacy (prefixed) [box-orient](https://www.w3.org/TR/2009/WD-css3-flexbox-20090723/#orientation) property. Partially equivalent to `flex-direction` in the standard syntax.
 */
export type BoxOrient =
  | "horizontal"
  | "vertical"
  | "inline-axis"
  | "block-axis";
/**
 * A value for the legacy (prefixed) [box-direction](https://www.w3.org/TR/2009/WD-css3-flexbox-20090723/#displayorder) property. Partially equivalent to the `flex-direction` property in the standard syntax.
 */
export type BoxDirection = "normal" | "reverse";
/**
 * A value for the legacy (prefixed) [box-align](https://www.w3.org/TR/2009/WD-css3-flexbox-20090723/#alignment) property. Equivalent to the `align-items` property in the standard syntax.
 */
export type BoxAlign = "start" | "end" | "center" | "baseline" | "stretch";
/**
 * A value for the legacy (prefixed) [box-pack](https://www.w3.org/TR/2009/WD-css3-flexbox-20090723/#packing) property. Equivalent to the `justify-content` property in the standard syntax.
 */
export type BoxPack = "start" | "end" | "center" | "justify";
/**
 * A value for the legacy (prefixed) [box-lines](https://www.w3.org/TR/2009/WD-css3-flexbox-20090723/#multiple) property. Equivalent to the `flex-wrap` property in the standard syntax.
 */
export type BoxLines = "single" | "multiple";
/**
 * A value for the legacy (prefixed) [flex-pack](https://www.w3.org/TR/2012/WD-css3-flexbox-20120322/#flex-pack) property. Equivalent to the `justify-content` property in the standard syntax.
 */
export type FlexPack = "start" | "end" | "center" | "justify" | "distribute";
/**
 * A value for the legacy (prefixed) [flex-item-align](https://www.w3.org/TR/2012/WD-css3-flexbox-20120322/#flex-align) property. Equivalent to the `align-self` property in the standard syntax.
 */
export type FlexItemAlign =
  | "auto"
  | "start"
  | "end"
  | "center"
  | "baseline"
  | "stretch";
/**
 * A value for the legacy (prefixed) [flex-line-pack](https://www.w3.org/TR/2012/WD-css3-flexbox-20120322/#flex-line-pack) property. Equivalent to the `align-content` property in the standard syntax.
 */
export type FlexLinePack =
  | "start"
  | "end"
  | "center"
  | "justify"
  | "distribute"
  | "stretch";
/**
 * A [track sizing](https://drafts.csswg.org/css-grid-2/#track-sizing) value for the `grid-template-rows` and `grid-template-columns` properties.
 */
export type TrackSizing =
  | {|
      type: "none",
    |}
  | {|
      /**
       * A list of grid track items.
       */
      items: TrackListItem[],

      /**
       * A list of line names.
       */
      lineNames: String[][],
      type: "track-list",
    |};
/**
 * Either a track size or `repeat()` function.
 *
 * See [TrackList](TrackList).
 */
export type TrackListItem =
  | {|
      type: "track-size",
      value: TrackSize,
    |}
  | {|
      type: "track-repeat",
      value: TrackRepeat,
    |};
/**
 * A [`<track-size>`](https://drafts.csswg.org/css-grid-2/#typedef-track-size) value, as used in the `grid-template-rows` and `grid-template-columns` properties.
 *
 * See [TrackListItem](TrackListItem).
 */
export type TrackSize =
  | {|
      type: "track-breadth",
      value: TrackBreadth,
    |}
  | {|
      /**
       * The maximum value.
       */
      max: TrackBreadth,

      /**
       * The minimum value.
       */
      min: TrackBreadth,
      type: "min-max",
    |}
  | {|
      type: "fit-content",
      value: DimensionPercentageFor_LengthValue,
    |};
/**
 * A [`<track-breadth>`](https://drafts.csswg.org/css-grid-2/#typedef-track-breadth) value.
 *
 * See [TrackSize](TrackSize).
 */
export type TrackBreadth =
  | {|
      type: "length",
      value: DimensionPercentageFor_LengthValue,
    |}
  | {|
      type: "flex",
      value: number,
    |}
  | {|
      type: "min-content",
    |}
  | {|
      type: "max-content",
    |}
  | {|
      type: "auto",
    |};
/**
 * A [`<repeat-count>`](https://drafts.csswg.org/css-grid-2/#typedef-track-repeat) value, used in the `repeat()` function.
 *
 * See [TrackRepeat](TrackRepeat).
 */
export type RepeatCount =
  | {|
      type: "number",
      value: number,
    |}
  | {|
      type: "auto-fill",
    |}
  | {|
      type: "auto-fit",
    |};
export type AutoFlowDirection = "row" | "column";
/**
 * A value for the [grid-template-areas](https://drafts.csswg.org/css-grid-2/#grid-template-areas-property) property.
 */
export type GridTemplateAreas =
  | {|
      type: "none",
    |}
  | {|
      /**
       * A flattened list of grid area names. Unnamed areas specified by the `.` token are represented as `None`.
       */
      areas: (string | null)[],

      /**
       * The number of columns in the grid.
       */
      columns: number,
      type: "areas",
    |};
/**
 * A [`<grid-line>`](https://drafts.csswg.org/css-grid-2/#typedef-grid-row-start-grid-line) value, used in the `grid-row-start`, `grid-row-end`, `grid-column-start`, and `grid-column-end` properties.
 */
export type GridLine =
  | {|
      type: "auto",
    |}
  | {|
      /**
       * A grid area name.
       */
      name: String,
      type: "area",
    |}
  | {|
      /**
       * A line number.
       */
      index: number,

      /**
       * A line name to filter by.
       */
      name?: String | null,
      type: "line",
    |}
  | {|
      /**
       * A line number.
       */
      index: number,

      /**
       * A line name to filter by.
       */
      name?: String | null,
      type: "span",
    |};
/**
 * A value for the [font-weight](https://www.w3.org/TR/css-fonts-4/#font-weight-prop) property.
 */
export type FontWeight =
  | {|
      type: "absolute",
      value: AbsoluteFontWeight,
    |}
  | {|
      type: "bolder",
    |}
  | {|
      type: "lighter",
    |};
/**
 * An [absolute font weight](https://www.w3.org/TR/css-fonts-4/#font-weight-absolute-values), as used in the `font-weight` property.
 *
 * See [FontWeight](FontWeight).
 */
export type AbsoluteFontWeight =
  | {|
      type: "weight",
      value: number,
    |}
  | {|
      type: "normal",
    |}
  | {|
      type: "bold",
    |};
/**
 * A value for the [font-size](https://www.w3.org/TR/css-fonts-4/#font-size-prop) property.
 */
export type FontSize =
  | {|
      type: "length",
      value: DimensionPercentageFor_LengthValue,
    |}
  | {|
      type: "absolute",
      value: AbsoluteFontSize,
    |}
  | {|
      type: "relative",
      value: RelativeFontSize,
    |};
/**
 * An [absolute font size](https://www.w3.org/TR/css-fonts-3/#absolute-size-value), as used in the `font-size` property.
 *
 * See [FontSize](FontSize).
 */
export type AbsoluteFontSize =
  | "xx-small"
  | "x-small"
  | "small"
  | "medium"
  | "large"
  | "x-large"
  | "xx-large"
  | "xxx-large";
/**
 * A [relative font size](https://www.w3.org/TR/css-fonts-3/#relative-size-value), as used in the `font-size` property.
 *
 * See [FontSize](FontSize).
 */
export type RelativeFontSize = "smaller" | "larger";
/**
 * A value for the [font-stretch](https://www.w3.org/TR/css-fonts-4/#font-stretch-prop) property.
 */
export type FontStretch =
  | {|
      type: "keyword",
      value: FontStretchKeyword,
    |}
  | {|
      type: "percentage",
      value: number,
    |};
/**
 * A [font stretch keyword](https://www.w3.org/TR/css-fonts-4/#font-stretch-prop), as used in the `font-stretch` property.
 *
 * See [FontStretch](FontStretch).
 */
export type FontStretchKeyword =
  | "normal"
  | "ultra-condensed"
  | "extra-condensed"
  | "condensed"
  | "semi-condensed"
  | "semi-expanded"
  | "expanded"
  | "extra-expanded"
  | "ultra-expanded";
/**
 * A value for the [font-family](https://www.w3.org/TR/css-fonts-4/#font-family-prop) property.
 */
export type FontFamily = GenericFontFamily | String;
/**
 * A [generic font family](https://www.w3.org/TR/css-fonts-4/#generic-font-families) name, as used in the `font-family` property.
 *
 * See [FontFamily](FontFamily).
 */
export type GenericFontFamily =
  | "serif"
  | "sans-serif"
  | "cursive"
  | "fantasy"
  | "monospace"
  | "system-ui"
  | "emoji"
  | "math"
  | "fangsong"
  | "ui-serif"
  | "ui-sans-serif"
  | "ui-monospace"
  | "ui-rounded"
  | "initial"
  | "inherit"
  | "unset"
  | "default"
  | "revert"
  | "revert-layer";
/**
 * A value for the [font-style](https://www.w3.org/TR/css-fonts-4/#font-style-prop) property.
 */
export type FontStyle =
  | {|
      type: "normal",
    |}
  | {|
      type: "italic",
    |}
  | {|
      type: "oblique",
      value: Angle,
    |};
/**
 * A value for the [font-variant-caps](https://www.w3.org/TR/css-fonts-4/#font-variant-caps-prop) property.
 */
export type FontVariantCaps =
  | "normal"
  | "small-caps"
  | "all-small-caps"
  | "petite-caps"
  | "all-petite-caps"
  | "unicase"
  | "titling-caps";
/**
 * A value for the [line-height](https://www.w3.org/TR/2020/WD-css-inline-3-20200827/#propdef-line-height) property.
 */
export type LineHeight =
  | {|
      type: "normal",
    |}
  | {|
      type: "number",
      value: number,
    |}
  | {|
      type: "length",
      value: DimensionPercentageFor_LengthValue,
    |};
/**
 * A value for the [vertical align](https://drafts.csswg.org/css2/#propdef-vertical-align) property.
 */
export type VerticalAlign =
  | {|
      type: "keyword",
      value: VerticalAlignKeyword,
    |}
  | {|
      type: "length",
      value: DimensionPercentageFor_LengthValue,
    |};
/**
 * A keyword for the [vertical align](https://drafts.csswg.org/css2/#propdef-vertical-align) property.
 */
export type VerticalAlignKeyword =
  | "baseline"
  | "sub"
  | "super"
  | "top"
  | "text-top"
  | "middle"
  | "bottom"
  | "text-bottom";
/**
 * A CSS [easing function](https://www.w3.org/TR/css-easing-1/#easing-functions).
 */
export type EasingFunction =
  | {|
      type: "linear",
    |}
  | {|
      type: "ease",
    |}
  | {|
      type: "ease-in",
    |}
  | {|
      type: "ease-out",
    |}
  | {|
      type: "ease-in-out",
    |}
  | {|
      type: "cubic-bezier",

      /**
       * The x-position of the first point in the curve.
       */
      x1: number,

      /**
       * The x-position of the second point in the curve.
       */
      x2: number,

      /**
       * The y-position of the first point in the curve.
       */
      y1: number,

      /**
       * The y-position of the second point in the curve.
       */
      y2: number,
    |}
  | {|
      /**
       * The number of intervals in the function.
       */
      count: number,

      /**
       * The step position.
       */
      position?: StepPosition,
      type: "steps",
    |};
/**
 * A [step position](https://www.w3.org/TR/css-easing-1/#step-position), used within the `steps()` function.
 */
export type StepPosition =
  | {|
      type: "start",
    |}
  | {|
      type: "end",
    |}
  | {|
      type: "jump-none",
    |}
  | {|
      type: "jump-both",
    |};
/**
 * A value for the [animation-iteration-count](https://drafts.csswg.org/css-animations/#animation-iteration-count) property.
 */
export type AnimationIterationCount =
  | {|
      type: "number",
      value: number,
    |}
  | {|
      type: "infinite",
    |};
/**
 * A value for the [animation-direction](https://drafts.csswg.org/css-animations/#animation-direction) property.
 */
export type AnimationDirection =
  | "normal"
  | "reverse"
  | "alternate"
  | "alternate-reverse";
/**
 * A value for the [animation-play-state](https://drafts.csswg.org/css-animations/#animation-play-state) property.
 */
export type AnimationPlayState = "running" | "paused";
/**
 * A value for the [animation-fill-mode](https://drafts.csswg.org/css-animations/#animation-fill-mode) property.
 */
export type AnimationFillMode = "none" | "forwards" | "backwards" | "both";
/**
 * A value for the [animation-composition](https://drafts.csswg.org/css-animations-2/#animation-composition) property.
 */
export type AnimationComposition = "replace" | "add" | "accumulate";
/**
 * A value for the [animation-timeline](https://drafts.csswg.org/css-animations-2/#animation-timeline) property.
 */
export type AnimationTimeline =
  | {|
      type: "auto",
    |}
  | {|
      type: "none",
    |}
  | {|
      type: "dashed-ident",
      value: String,
    |}
  | {|
      type: "scroll",
      value: ScrollTimeline,
    |}
  | {|
      type: "view",
      value: ViewTimeline,
    |};
/**
 * A scroll axis, used in the `scroll()` function.
 */
export type ScrollAxis = "block" | "inline" | "x" | "y";
/**
 * A scroller, used in the `scroll()` function.
 */
export type Scroller = "root" | "nearest" | "self";
/**
 * A generic value that represents a value with two components, e.g. a border radius.
 *
 * When serialized, only a single component will be written if both are equal.
 * @minItems 2
 * @maxItems 2
 */
export type Size2DFor_LengthPercentageOrAuto = [
  LengthPercentageOrAuto,
  LengthPercentageOrAuto
];
/**
 * A value for the [animation-range-start](https://drafts.csswg.org/scroll-animations/#animation-range-start) property.
 */
export type AnimationRangeStart = AnimationAttachmentRange;
/**
 * A value for the [animation-range-start](https://drafts.csswg.org/scroll-animations/#animation-range-start) or [animation-range-end](https://drafts.csswg.org/scroll-animations/#animation-range-end) property.
 */
export type AnimationAttachmentRange =
  | "normal"
  | DimensionPercentageFor_LengthValue
  | {|
      /**
       * The name of the timeline range.
       */
      name: TimelineRangeName,

      /**
       * The offset from the start of the named timeline range.
       */
      offset: DimensionPercentageFor_LengthValue,
    |};
/**
 * A [view progress timeline range](https://drafts.csswg.org/scroll-animations/#view-timelines-ranges)
 */
export type TimelineRangeName =
  | "cover"
  | "contain"
  | "entry"
  | "exit"
  | "entry-crossing"
  | "exit-crossing";
/**
 * A value for the [animation-range-end](https://drafts.csswg.org/scroll-animations/#animation-range-end) property.
 */
export type AnimationRangeEnd = AnimationAttachmentRange;
/**
 * An individual [transform function](https://www.w3.org/TR/2019/CR-css-transforms-1-20190214/#two-d-transform-functions).
 */
export type Transform =
  | {|
      type: "translate",

      /**
       * @minItems 2
       * @maxItems 2
       */
      value: [
        DimensionPercentageFor_LengthValue,
        DimensionPercentageFor_LengthValue
      ],
    |}
  | {|
      type: "translateX",
      value: DimensionPercentageFor_LengthValue,
    |}
  | {|
      type: "translateY",
      value: DimensionPercentageFor_LengthValue,
    |}
  | {|
      type: "translateZ",
      value: Length,
    |}
  | {|
      type: "translate3d",

      /**
       * @minItems 3
       * @maxItems 3
       */
      value: [
        DimensionPercentageFor_LengthValue,
        DimensionPercentageFor_LengthValue,
        Length
      ],
    |}
  | {|
      type: "scale",

      /**
       * @minItems 2
       * @maxItems 2
       */
      value: [NumberOrPercentage, NumberOrPercentage],
    |}
  | {|
      type: "scaleX",
      value: NumberOrPercentage,
    |}
  | {|
      type: "scaleY",
      value: NumberOrPercentage,
    |}
  | {|
      type: "scaleZ",
      value: NumberOrPercentage,
    |}
  | {|
      type: "scale3d",

      /**
       * @minItems 3
       * @maxItems 3
       */
      value: [NumberOrPercentage, NumberOrPercentage, NumberOrPercentage],
    |}
  | {|
      type: "rotate",
      value: Angle,
    |}
  | {|
      type: "rotateX",
      value: Angle,
    |}
  | {|
      type: "rotateY",
      value: Angle,
    |}
  | {|
      type: "rotateZ",
      value: Angle,
    |}
  | {|
      type: "rotate3d",

      /**
       * @minItems 4
       * @maxItems 4
       */
      value: [number, number, number, Angle],
    |}
  | {|
      type: "skew",

      /**
       * @minItems 2
       * @maxItems 2
       */
      value: [Angle, Angle],
    |}
  | {|
      type: "skewX",
      value: Angle,
    |}
  | {|
      type: "skewY",
      value: Angle,
    |}
  | {|
      type: "perspective",
      value: Length,
    |}
  | {|
      type: "matrix",
      value: MatrixForFloat,
    |}
  | {|
      type: "matrix3d",
      value: Matrix3DForFloat,
    |};
/**
 * A value for the [transform-style](https://drafts.csswg.org/css-transforms-2/#transform-style-property) property.
 */
export type TransformStyle = "flat" | "preserve3d";
/**
 * A value for the [transform-box](https://drafts.csswg.org/css-transforms-1/#transform-box) property.
 */
export type TransformBox =
  | "content-box"
  | "border-box"
  | "fill-box"
  | "stroke-box"
  | "view-box";
/**
 * A value for the [backface-visibility](https://drafts.csswg.org/css-transforms-2/#backface-visibility-property) property.
 */
export type BackfaceVisibility = "visible" | "hidden";
/**
 * A value for the [perspective](https://drafts.csswg.org/css-transforms-2/#perspective-property) property.
 */
export type Perspective =
  | {|
      type: "none",
    |}
  | {|
      type: "length",
      value: Length,
    |};
/**
 * A value for the [translate](https://drafts.csswg.org/css-transforms-2/#propdef-translate) property.
 */
export type Translate =
  | "none"
  | {|
      /**
       * The x translation.
       */
      x: DimensionPercentageFor_LengthValue,

      /**
       * The y translation.
       */
      y: DimensionPercentageFor_LengthValue,

      /**
       * The z translation.
       */
      z: Length,
    |};
/**
 * A value for the [scale](https://drafts.csswg.org/css-transforms-2/#propdef-scale) property.
 */
export type Scale =
  | "none"
  | {|
      /**
       * Scale on the x axis.
       */
      x: NumberOrPercentage,

      /**
       * Scale on the y axis.
       */
      y: NumberOrPercentage,

      /**
       * Scale on the z axis.
       */
      z: NumberOrPercentage,
    |};
/**
 * Defines how text case should be transformed in the [text-transform](https://www.w3.org/TR/2021/CRD-css-text-3-20210422/#text-transform-property) property.
 */
export type TextTransformCase =
  | "none"
  | "uppercase"
  | "lowercase"
  | "capitalize";
/**
 * A value for the [white-space](https://www.w3.org/TR/2021/CRD-css-text-3-20210422/#white-space-property) property.
 */
export type WhiteSpace =
  | "normal"
  | "pre"
  | "nowrap"
  | "pre-wrap"
  | "break-spaces"
  | "pre-line";
/**
 * A value for the [word-break](https://www.w3.org/TR/2021/CRD-css-text-3-20210422/#word-break-property) property.
 */
export type WordBreak = "normal" | "keep-all" | "break-all" | "break-word";
/**
 * A value for the [line-break](https://www.w3.org/TR/2021/CRD-css-text-3-20210422/#line-break-property) property.
 */
export type LineBreak = "auto" | "loose" | "normal" | "strict" | "anywhere";
/**
 * A value for the [hyphens](https://www.w3.org/TR/2021/CRD-css-text-3-20210422/#hyphenation) property.
 */
export type Hyphens = "none" | "manual" | "auto";
/**
 * A value for the [overflow-wrap](https://www.w3.org/TR/2021/CRD-css-text-3-20210422/#overflow-wrap-property) property.
 */
export type OverflowWrap = "normal" | "anywhere" | "break-word";
/**
 * A value for the [text-align](https://www.w3.org/TR/2021/CRD-css-text-3-20210422/#text-align-property) property.
 */
export type TextAlign =
  | "start"
  | "end"
  | "left"
  | "right"
  | "center"
  | "justify"
  | "match-parent"
  | "justify-all";
/**
 * A value for the [text-align-last](https://www.w3.org/TR/2021/CRD-css-text-3-20210422/#text-align-last-property) property.
 */
export type TextAlignLast =
  | "auto"
  | "start"
  | "end"
  | "left"
  | "right"
  | "center"
  | "justify"
  | "match-parent";
/**
 * A value for the [text-justify](https://www.w3.org/TR/2021/CRD-css-text-3-20210422/#text-justify-property) property.
 */
export type TextJustify = "auto" | "none" | "inter-word" | "inter-character";
/**
 * A value for the [word-spacing](https://www.w3.org/TR/2021/CRD-css-text-3-20210422/#word-spacing-property) and [letter-spacing](https://www.w3.org/TR/2021/CRD-css-text-3-20210422/#letter-spacing-property) properties.
 */
export type Spacing =
  | {|
      type: "normal",
    |}
  | {|
      type: "length",
      value: Length,
    |};
export type TextDecorationLine =
  | ExclusiveTextDecorationLine
  | OtherTextDecorationLine[];
export type ExclusiveTextDecorationLine =
  | "none"
  | "spelling-error"
  | "grammar-error";
export type OtherTextDecorationLine =
  | "underline"
  | "overline"
  | "line-through"
  | "blink";
/**
 * A value for the [text-decoration-style](https://www.w3.org/TR/2020/WD-css-text-decor-4-20200506/#text-decoration-style-property) property.
 */
export type TextDecorationStyle =
  | "solid"
  | "double"
  | "dotted"
  | "dashed"
  | "wavy";
/**
 * A value for the [text-decoration-thickness](https://www.w3.org/TR/2020/WD-css-text-decor-4-20200506/#text-decoration-width-property) property.
 */
export type TextDecorationThickness =
  | {|
      type: "auto",
    |}
  | {|
      type: "from-font",
    |}
  | {|
      type: "length-percentage",
      value: DimensionPercentageFor_LengthValue,
    |};
/**
 * A value for the [text-decoration-skip-ink](https://www.w3.org/TR/2020/WD-css-text-decor-4-20200506/#text-decoration-skip-ink-property) property.
 */
export type TextDecorationSkipInk = "auto" | "none" | "all";
/**
 * A value for the [text-emphasis-style](https://www.w3.org/TR/2020/WD-css-text-decor-4-20200506/#text-emphasis-style-property) property.
 */
export type TextEmphasisStyle =
  | {|
      type: "none",
    |}
  | {|
      /**
       * The fill mode for the marks.
       */
      fill: TextEmphasisFillMode,

      /**
       * The shape of the marks.
       */
      shape?: TextEmphasisShape | null,
      type: "keyword",
    |}
  | {|
      type: "string",
      value: String,
    |};
/**
 * A keyword for the [text-emphasis-style](https://www.w3.org/TR/2020/WD-css-text-decor-4-20200506/#text-emphasis-style-property) property.
 *
 * See [TextEmphasisStyle](TextEmphasisStyle).
 */
export type TextEmphasisFillMode = "filled" | "open";
/**
 * A text emphasis shape for the [text-emphasis-style](https://www.w3.org/TR/2020/WD-css-text-decor-4-20200506/#text-emphasis-style-property) property.
 *
 * See [TextEmphasisStyle](TextEmphasisStyle).
 */
export type TextEmphasisShape =
  | "dot"
  | "circle"
  | "double-circle"
  | "triangle"
  | "sesame";
/**
 * A horizontal position keyword for the [text-emphasis-position](https://www.w3.org/TR/2020/WD-css-text-decor-4-20200506/#text-emphasis-position-property) property.
 *
 * See [TextEmphasisPosition](TextEmphasisPosition).
 */
export type TextEmphasisPositionHorizontal = "left" | "right";
/**
 * A vertical position keyword for the [text-emphasis-position](https://www.w3.org/TR/2020/WD-css-text-decor-4-20200506/#text-emphasis-position-property) property.
 *
 * See [TextEmphasisPosition](TextEmphasisPosition).
 */
export type TextEmphasisPositionVertical = "over" | "under";
/**
 * A value for the [text-size-adjust](https://w3c.github.io/csswg-drafts/css-size-adjust/#adjustment-control) property.
 */
export type TextSizeAdjust =
  | {|
      type: "auto",
    |}
  | {|
      type: "none",
    |}
  | {|
      type: "percentage",
      value: number,
    |};
/**
 * A value for the [direction](https://drafts.csswg.org/css-writing-modes-3/#direction) property.
 */
export type Direction2 = "ltr" | "rtl";
/**
 * A value for the [unicode-bidi](https://drafts.csswg.org/css-writing-modes-3/#unicode-bidi) property.
 */
export type UnicodeBidi =
  | "normal"
  | "embed"
  | "isolate"
  | "bidi-override"
  | "isolate-override"
  | "plaintext";
/**
 * A value for the [box-decoration-break](https://www.w3.org/TR/css-break-3/#break-decoration) property.
 */
export type BoxDecorationBreak = "slice" | "clone";
/**
 * A value for the [resize](https://www.w3.org/TR/2021/WD-css-ui-4-20210316/#resize) property.
 */
export type Resize =
  | "none"
  | "both"
  | "horizontal"
  | "vertical"
  | "block"
  | "inline";
/**
 * A pre-defined [cursor](https://www.w3.org/TR/2021/WD-css-ui-4-20210316/#cursor) value, used in the `cursor` property.
 *
 * See [Cursor](Cursor).
 */
export type CursorKeyword =
  | "auto"
  | "default"
  | "none"
  | "context-menu"
  | "help"
  | "pointer"
  | "progress"
  | "wait"
  | "cell"
  | "crosshair"
  | "text"
  | "vertical-text"
  | "alias"
  | "copy"
  | "move"
  | "no-drop"
  | "not-allowed"
  | "grab"
  | "grabbing"
  | "e-resize"
  | "n-resize"
  | "ne-resize"
  | "nw-resize"
  | "s-resize"
  | "se-resize"
  | "sw-resize"
  | "w-resize"
  | "ew-resize"
  | "ns-resize"
  | "nesw-resize"
  | "nwse-resize"
  | "col-resize"
  | "row-resize"
  | "all-scroll"
  | "zoom-in"
  | "zoom-out";
/**
 * A value for the [caret-color](https://www.w3.org/TR/2021/WD-css-ui-4-20210316/#caret-color) property.
 */
export type ColorOrAuto =
  | {|
      type: "auto",
    |}
  | {|
      type: "color",
      value: CssColor,
    |};
/**
 * A value for the [caret-shape](https://www.w3.org/TR/2021/WD-css-ui-4-20210316/#caret-shape) property.
 */
export type CaretShape = "auto" | "bar" | "block" | "underscore";
/**
 * A value for the [user-select](https://www.w3.org/TR/2021/WD-css-ui-4-20210316/#content-selection) property.
 */
export type UserSelect = "auto" | "text" | "none" | "contain" | "all";
export type Appearance = string;
/**
 * A value for the [list-style-type](https://www.w3.org/TR/2020/WD-css-lists-3-20201117/#text-markers) property.
 */
export type ListStyleType =
  | {|
      type: "none",
    |}
  | {|
      type: "string",
      value: String,
    |}
  | {|
      type: "counter-style",
      value: CounterStyle,
    |};
/**
 * A [counter-style](https://www.w3.org/TR/css-counter-styles-3/#typedef-counter-style) name.
 */
export type CounterStyle =
  | {|
      type: "predefined",
      value: PredefinedCounterStyle,
    |}
  | {|
      type: "name",
      value: String,
    |}
  | {|
      /**
       * The symbols.
       */
      symbols: Symbol[],

      /**
       * The counter system.
       */
      system?: {| ...SymbolsType, ...string |},
      type: "symbols",
    |};
/**
 * A [predefined counter](https://www.w3.org/TR/css-counter-styles-3/#predefined-counters) style.
 */
export type PredefinedCounterStyle =
  | "decimal"
  | "decimal-leading-zero"
  | "arabic-indic"
  | "armenian"
  | "upper-armenian"
  | "lower-armenian"
  | "bengali"
  | "cambodian"
  | "khmer"
  | "cjk-decimal"
  | "devanagari"
  | "georgian"
  | "gujarati"
  | "gurmukhi"
  | "hebrew"
  | "kannada"
  | "lao"
  | "malayalam"
  | "mongolian"
  | "myanmar"
  | "oriya"
  | "persian"
  | "lower-roman"
  | "upper-roman"
  | "tamil"
  | "telugu"
  | "thai"
  | "tibetan"
  | "lower-alpha"
  | "lower-latin"
  | "upper-alpha"
  | "upper-latin"
  | "lower-greek"
  | "hiragana"
  | "hiragana-iroha"
  | "katakana"
  | "katakana-iroha"
  | "disc"
  | "circle"
  | "square"
  | "disclosure-open"
  | "disclosure-closed"
  | "cjk-earthly-branch"
  | "cjk-heavenly-stem"
  | "japanese-informal"
  | "japanese-formal"
  | "korean-hangul-formal"
  | "korean-hanja-informal"
  | "korean-hanja-formal"
  | "simp-chinese-informal"
  | "simp-chinese-formal"
  | "trad-chinese-informal"
  | "trad-chinese-formal"
  | "ethiopic-numeric";
/**
 * A single [symbol](https://www.w3.org/TR/css-counter-styles-3/#funcdef-symbols) as used in the `symbols()` function.
 *
 * See [CounterStyle](CounterStyle).
 */
export type Symbol =
  | {|
      type: "string",
      value: String,
    |}
  | {|
      type: "image",
      value: Image,
    |};
/**
 * A [`<symbols-type>`](https://www.w3.org/TR/css-counter-styles-3/#typedef-symbols-type) value, as used in the `symbols()` function.
 *
 * See [CounterStyle](CounterStyle).
 */
export type SymbolsType =
  | "cyclic"
  | "numeric"
  | "alphabetic"
  | "symbolic"
  | "fixed";
/**
 * A value for the [list-style-position](https://www.w3.org/TR/2020/WD-css-lists-3-20201117/#list-style-position-property) property.
 */
export type ListStylePosition = "inside" | "outside";
/**
 * A value for the [marker-side](https://www.w3.org/TR/2020/WD-css-lists-3-20201117/#marker-side) property.
 */
export type MarkerSide = "match-self" | "match-parent";
/**
 * An SVG [`<paint>`](https://www.w3.org/TR/SVG2/painting.html#SpecifyingPaint) value used in the `fill` and `stroke` properties.
 */
export type SVGPaint =
  | {|
      /**
       * A fallback to be used used in case the paint server cannot be resolved.
       */
      fallback?: SVGPaintFallback | null,
      type: "url",

      /**
       * The url of the paint server.
       */
      url: Url,
    |}
  | {|
      type: "color",
      value: CssColor,
    |}
  | {|
      type: "context-fill",
    |}
  | {|
      type: "context-stroke",
    |}
  | {|
      type: "none",
    |};
/**
 * A fallback for an SVG paint in case a paint server `url()` cannot be resolved.
 *
 * See [SVGPaint](SVGPaint).
 */
export type SVGPaintFallback =
  | {|
      type: "none",
    |}
  | {|
      type: "color",
      value: CssColor,
    |};
/**
 * A [`<fill-rule>`](https://www.w3.org/TR/css-shapes-1/#typedef-fill-rule) used to determine the interior of a `polygon()` shape.
 *
 * See [Polygon](Polygon).
 */
export type FillRule = "nonzero" | "evenodd";
/**
 * A value for the [stroke-linecap](https://www.w3.org/TR/SVG2/painting.html#LineCaps) property.
 */
export type StrokeLinecap = "butt" | "round" | "square";
/**
 * A value for the [stroke-linejoin](https://www.w3.org/TR/SVG2/painting.html#LineJoin) property.
 */
export type StrokeLinejoin =
  | "miter"
  | "miter-clip"
  | "round"
  | "bevel"
  | "arcs";
/**
 * A value for the [stroke-dasharray](https://www.w3.org/TR/SVG2/painting.html#StrokeDashing) property.
 */
export type StrokeDasharray =
  | {|
      type: "none",
    |}
  | {|
      type: "values",
      value: DimensionPercentageFor_LengthValue[],
    |};
/**
 * A value for the [marker](https://www.w3.org/TR/SVG2/painting.html#VertexMarkerProperties) properties.
 */
export type Marker =
  | {|
      type: "none",
    |}
  | {|
      type: "url",
      value: Url,
    |};
/**
 * A value for the [color-interpolation](https://www.w3.org/TR/SVG2/painting.html#ColorInterpolation) property.
 */
export type ColorInterpolation = "auto" | "srgb" | "linearrgb";
/**
 * A value for the [color-rendering](https://www.w3.org/TR/SVG2/painting.html#ColorRendering) property.
 */
export type ColorRendering = "auto" | "optimizespeed" | "optimizequality";
/**
 * A value for the [shape-rendering](https://www.w3.org/TR/SVG2/painting.html#ShapeRendering) property.
 */
export type ShapeRendering =
  | "auto"
  | "optimizespeed"
  | "crispedges"
  | "geometricprecision";
/**
 * A value for the [text-rendering](https://www.w3.org/TR/SVG2/painting.html#TextRendering) property.
 */
export type TextRendering =
  | "auto"
  | "optimizespeed"
  | "optimizelegibility"
  | "geometricprecision";
/**
 * A value for the [image-rendering](https://www.w3.org/TR/SVG2/painting.html#ImageRendering) property.
 */
export type ImageRendering = "auto" | "optimizespeed" | "optimizequality";
/**
 * A value for the [clip-path](https://www.w3.org/TR/css-masking-1/#the-clip-path) property.
 */
export type ClipPath =
  | {|
      type: "none",
    |}
  | {|
      type: "url",
      value: Url,
    |}
  | {|
      /**
       * A reference box that the shape is positioned according to.
       */
      referenceBox: GeometryBox,

      /**
       * A basic shape.
       */
      shape: BasicShape,
      type: "shape",
    |}
  | {|
      type: "box",
      value: GeometryBox,
    |};
/**
 * A [`<geometry-box>`](https://www.w3.org/TR/css-masking-1/#typedef-geometry-box) value as used in the `mask-clip` and `clip-path` properties.
 */
export type GeometryBox =
  | "border-box"
  | "padding-box"
  | "content-box"
  | "margin-box"
  | "fill-box"
  | "stroke-box"
  | "view-box";
/**
 * A CSS [`<basic-shape>`](https://www.w3.org/TR/css-shapes-1/#basic-shape-functions) value.
 */
export type BasicShape =
  | {|
      type: "inset",
      value: InsetRect,
    |}
  | {|
      type: "circle",
      value: Circle2,
    |}
  | {|
      type: "ellipse",
      value: Ellipse2,
    |}
  | {|
      type: "polygon",
      value: Polygon,
    |};
/**
 * A generic value that represents a value for four sides of a box, e.g. border-width, margin, padding, etc.
 *
 * When serialized, as few components as possible are written when there are duplicate values.
 * @minItems 4
 * @maxItems 4
 */
export type RectFor_DimensionPercentageFor_LengthValue = [
  DimensionPercentageFor_LengthValue,
  DimensionPercentageFor_LengthValue,
  DimensionPercentageFor_LengthValue,
  DimensionPercentageFor_LengthValue
];
/**
 * A [`<shape-radius>`](https://www.w3.org/TR/css-shapes-1/#typedef-shape-radius) value that defines the radius of a `circle()` or `ellipse()` shape.
 */
export type ShapeRadius =
  | {|
      type: "length-percentage",
      value: DimensionPercentageFor_LengthValue,
    |}
  | {|
      type: "closest-side",
    |}
  | {|
      type: "farthest-side",
    |};
/**
 * A value for the [mask-mode](https://www.w3.org/TR/css-masking-1/#the-mask-mode) property.
 */
export type MaskMode = "luminance" | "alpha" | "match-source";
/**
 * A value for the [mask-clip](https://www.w3.org/TR/css-masking-1/#the-mask-clip) property.
 */
export type MaskClip =
  | {|
      type: "geometry-box",
      value: GeometryBox,
    |}
  | {|
      type: "no-clip",
    |};
/**
 * A value for the [mask-composite](https://www.w3.org/TR/css-masking-1/#the-mask-composite) property.
 */
export type MaskComposite = "add" | "subtract" | "intersect" | "exclude";
/**
 * A value for the [mask-type](https://www.w3.org/TR/css-masking-1/#the-mask-type) property.
 */
export type MaskType = "luminance" | "alpha";
/**
 * A value for the [mask-border-mode](https://www.w3.org/TR/css-masking-1/#the-mask-border-mode) property.
 */
export type MaskBorderMode = "luminance" | "alpha";
/**
 * A value for the [-webkit-mask-composite](https://developer.mozilla.org/en-US/docs/Web/CSS/-webkit-mask-composite) property.
 *
 * See also [MaskComposite](MaskComposite).
 */
export type WebKitMaskComposite =
  | (
      | "clear"
      | "copy"
      | "source-atop"
      | "destination-over"
      | "destination-in"
      | "destination-out"
      | "destination-atop"
    )
  | "source-over"
  | "source-in"
  | "source-out"
  | "xor";
/**
 * A value for the [-webkit-mask-source-type](https://github.com/WebKit/WebKit/blob/6eece09a1c31e47489811edd003d1e36910e9fd3/Source/WebCore/css/CSSProperties.json#L6578-L6587) property.
 *
 * See also [MaskMode](MaskMode).
 */
export type WebKitMaskSourceType = "auto" | "luminance" | "alpha";
/**
 * A value for the [filter](https://drafts.fxtf.org/filter-effects-1/#FilterProperty) and [backdrop-filter](https://drafts.fxtf.org/filter-effects-2/#BackdropFilterProperty) properties.
 */
export type FilterList =
  | {|
      type: "none",
    |}
  | {|
      type: "filters",
      value: Filter[],
    |};
/**
 * A [filter](https://drafts.fxtf.org/filter-effects-1/#filter-functions) function.
 */
export type Filter =
  | {|
      type: "blur",
      value: Length,
    |}
  | {|
      type: "brightness",
      value: NumberOrPercentage,
    |}
  | {|
      type: "contrast",
      value: NumberOrPercentage,
    |}
  | {|
      type: "grayscale",
      value: NumberOrPercentage,
    |}
  | {|
      type: "hue-rotate",
      value: Angle,
    |}
  | {|
      type: "invert",
      value: NumberOrPercentage,
    |}
  | {|
      type: "opacity",
      value: NumberOrPercentage,
    |}
  | {|
      type: "saturate",
      value: NumberOrPercentage,
    |}
  | {|
      type: "sepia",
      value: NumberOrPercentage,
    |}
  | {|
      type: "drop-shadow",
      value: DropShadow,
    |}
  | {|
      type: "url",
      value: Url,
    |};
/**
 * A value for the [z-index](https://drafts.csswg.org/css2/#z-index) property.
 */
export type ZIndex =
  | {|
      type: "auto",
    |}
  | {|
      type: "integer",
      value: number,
    |};
/**
 * A value for the [container-type](https://drafts.csswg.org/css-contain-3/#container-type) property. Establishes the element as a query container for the purpose of container queries.
 */
export type ContainerType = "normal" | "inline-size" | "size";
/**
 * A value for the [container-name](https://drafts.csswg.org/css-contain-3/#container-name) property.
 */
export type ContainerNameList =
  | {|
      type: "none",
    |}
  | {|
      type: "names",
      value: String[],
    |};
/**
 * A value for the [view-transition-name](https://drafts.csswg.org/css-view-transitions-1/#view-transition-name-prop) property.
 */
export type ViewTransitionName = "none" | "auto" | String;
/**
 * The `none` keyword, or a space-separated list of custom idents.
 */
export type NoneOrCustomIdentList = "none" | String[];
/**
 * A value for the [view-transition-group](https://drafts.csswg.org/css-view-transitions-2/#view-transition-group-prop) property.
 */
export type ViewTransitionGroup = "normal" | "contain" | "nearest" | String;
/**
 * A [CSS-wide keyword](https://drafts.csswg.org/css-cascade-5/#defaulting-keywords).
 */
export type CSSWideKeyword =
  | "initial"
  | "inherit"
  | "unset"
  | "revert"
  | "revert-layer";
/**
 * A CSS custom property name.
 */
export type CustomPropertyName = String | String;
export type SelectorComponent =
  | {|
      type: "combinator",
      value: Combinator,
    |}
  | {|
      type: "universal",
    |}
  | (
      | {|
          type: "namespace",
          kind: "none",
        |}
      | {|
          type: "namespace",
          kind: "any",
        |}
      | {|
          type: "namespace",
          kind: "named",
          prefix: string,
        |}
    )
  | {|
      name: string,
      type: "type",
    |}
  | {|
      name: string,
      type: "id",
    |}
  | {|
      name: string,
      type: "class",
    |}
  | {|
      name: string,
      namespace?: NamespaceConstraint | null,
      operation?: AttrOperation | null,
      type: "attribute",
    |}
  | {|
      ...{|
        type: "pseudo-class",
      |},
      ...TSPseudoClass | PseudoClass,
    |}
  | {|
      ...{|
        type: "pseudo-element",
      |},
      ...BuiltinPseudoElement | PseudoElement,
    |}
  | {|
      type: "nesting",
    |};
export type Combinator =
  | ("child" | "descendant" | "next-sibling" | "later-sibling")
  | "pseudo-element"
  | "slot-assignment"
  | "part"
  | "deep-descendant"
  | "deep";
export type NamespaceConstraint =
  | {|
      type: "any",
    |}
  | {|
      prefix: string,
      type: "specific",
      url: string,
    |};
export type ParsedCaseSensitivity =
  | "explicit-case-sensitive"
  | "ascii-case-insensitive"
  | "case-sensitive"
  | "ascii-case-insensitive-if-in-html-element-in-html-document";
export type AttrSelectorOperator =
  | "equal"
  | "includes"
  | "dash-match"
  | "prefix"
  | "substring"
  | "suffix";
export type TSPseudoClass =
  | {|
      kind: "not",
      selectors: Selector[],
    |}
  | {|
      kind: "first-child",
    |}
  | {|
      kind: "last-child",
    |}
  | {|
      kind: "only-child",
    |}
  | {|
      kind: "root",
    |}
  | {|
      kind: "empty",
    |}
  | {|
      kind: "scope",
    |}
  | {|
      a: number,
      b: number,
      kind: "nth-child",
      of?: Selector[] | null,
    |}
  | {|
      a: number,
      b: number,
      kind: "nth-last-child",
      of?: Selector[] | null,
    |}
  | {|
      a: number,
      b: number,
      kind: "nth-col",
    |}
  | {|
      a: number,
      b: number,
      kind: "nth-last-col",
    |}
  | {|
      a: number,
      b: number,
      kind: "nth-of-type",
    |}
  | {|
      a: number,
      b: number,
      kind: "nth-last-of-type",
    |}
  | {|
      kind: "first-of-type",
    |}
  | {|
      kind: "last-of-type",
    |}
  | {|
      kind: "only-of-type",
    |}
  | {|
      kind: "host",
      selectors?: Selector | null,
    |}
  | {|
      kind: "where",
      selectors: Selector[],
    |}
  | {|
      kind: "is",
      selectors: Selector[],
    |}
  | {|
      kind: "any",
      selectors: Selector[],
      vendorPrefix: VendorPrefix,
    |}
  | {|
      kind: "has",
      selectors: Selector[],
    |};
/**
 * A pseudo class.
 */
export type PseudoClass =
  | {|
      kind: "lang",

      /**
       * A list of language codes.
       */
      languages: String[],
    |}
  | {|
      /**
       * A direction.
       */
      direction: Direction,
      kind: "dir",
    |}
  | {|
      kind: "hover",
    |}
  | {|
      kind: "active",
    |}
  | {|
      kind: "focus",
    |}
  | {|
      kind: "focus-visible",
    |}
  | {|
      kind: "focus-within",
    |}
  | {|
      kind: "current",
    |}
  | {|
      kind: "past",
    |}
  | {|
      kind: "future",
    |}
  | {|
      kind: "playing",
    |}
  | {|
      kind: "paused",
    |}
  | {|
      kind: "seeking",
    |}
  | {|
      kind: "buffering",
    |}
  | {|
      kind: "stalled",
    |}
  | {|
      kind: "muted",
    |}
  | {|
      kind: "volume-locked",
    |}
  | {|
      kind: "fullscreen",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      kind: "open",
    |}
  | {|
      kind: "closed",
    |}
  | {|
      kind: "modal",
    |}
  | {|
      kind: "picture-in-picture",
    |}
  | {|
      kind: "popover-open",
    |}
  | {|
      kind: "defined",
    |}
  | {|
      kind: "any-link",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      kind: "link",
    |}
  | {|
      kind: "local-link",
    |}
  | {|
      kind: "target",
    |}
  | {|
      kind: "target-within",
    |}
  | {|
      kind: "visited",
    |}
  | {|
      kind: "enabled",
    |}
  | {|
      kind: "disabled",
    |}
  | {|
      kind: "read-only",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      kind: "read-write",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      kind: "placeholder-shown",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      kind: "default",
    |}
  | {|
      kind: "checked",
    |}
  | {|
      kind: "indeterminate",
    |}
  | {|
      kind: "blank",
    |}
  | {|
      kind: "valid",
    |}
  | {|
      kind: "invalid",
    |}
  | {|
      kind: "in-range",
    |}
  | {|
      kind: "out-of-range",
    |}
  | {|
      kind: "required",
    |}
  | {|
      kind: "optional",
    |}
  | {|
      kind: "user-valid",
    |}
  | {|
      kind: "user-invalid",
    |}
  | {|
      kind: "autofill",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      kind: "active-view-transition",
    |}
  | {|
      kind: "active-view-transition-type",

      /**
       * A view transition type.
       */
      type: String[],
    |}
  | {|
      kind: "local",

      /**
       * A local selector.
       */
      selector: Selector,
    |}
  | {|
      kind: "global",

      /**
       * A global selector.
       */
      selector: Selector,
    |}
  | {|
      kind: "webkit-scrollbar",
      value: WebKitScrollbarPseudoClass,
    |}
  | {|
      kind: "custom",

      /**
       * The pseudo class name.
       */
      name: String,
    |}
  | {|
      /**
       * The arguments of the pseudo class function.
       */
      arguments: TokenOrValue[],
      kind: "custom-function",

      /**
       * The pseudo class name.
       */
      name: String,
    |};
/**
 * The [:dir()](https://drafts.csswg.org/selectors-4/#the-dir-pseudo) pseudo class.
 */
export type Direction = "ltr" | "rtl";
/**
 * A [webkit scrollbar](https://webkit.org/blog/363/styling-scrollbars/) pseudo class.
 */
export type WebKitScrollbarPseudoClass =
  | "horizontal"
  | "vertical"
  | "decrement"
  | "increment"
  | "start"
  | "end"
  | "double-button"
  | "single-button"
  | "no-button"
  | "corner-present"
  | "window-inactive";
export type BuiltinPseudoElement =
  | {|
      kind: "slotted",
      selector: Selector,
    |}
  | {|
      kind: "part",
      names: string[],
    |};
/**
 * A pseudo element.
 */
export type PseudoElement =
  | {|
      kind: "after",
    |}
  | {|
      kind: "before",
    |}
  | {|
      kind: "first-line",
    |}
  | {|
      kind: "first-letter",
    |}
  | {|
      kind: "details-content",
    |}
  | {|
      kind: "target-text",
    |}
  | {|
      kind: "selection",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      kind: "placeholder",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      kind: "marker",
    |}
  | {|
      kind: "backdrop",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      kind: "file-selector-button",
      vendorPrefix: VendorPrefix,
    |}
  | {|
      kind: "webkit-scrollbar",
      value: WebKitScrollbarPseudoElement,
    |}
  | {|
      kind: "cue",
    |}
  | {|
      kind: "cue-region",
    |}
  | {|
      kind: "cue-function",

      /**
       * The selector argument.
       */
      selector: Selector,
    |}
  | {|
      kind: "cue-region-function",

      /**
       * The selector argument.
       */
      selector: Selector,
    |}
  | {|
      kind: "view-transition",
    |}
  | {|
      kind: "view-transition-group",

      /**
       * A part name selector.
       */
      part: ViewTransitionPartSelector,
    |}
  | {|
      kind: "view-transition-image-pair",

      /**
       * A part name selector.
       */
      part: ViewTransitionPartSelector,
    |}
  | {|
      kind: "view-transition-old",

      /**
       * A part name selector.
       */
      part: ViewTransitionPartSelector,
    |}
  | {|
      kind: "view-transition-new",

      /**
       * A part name selector.
       */
      part: ViewTransitionPartSelector,
    |}
  | {|
      kind: "custom",

      /**
       * The name of the pseudo element.
       */
      name: String,
    |}
  | {|
      /**
       * The arguments of the pseudo element function.
       */
      arguments: TokenOrValue[],
      kind: "custom-function",

      /**
       * The name of the pseudo element.
       */
      name: String,
    |};
/**
 * A [webkit scrollbar](https://webkit.org/blog/363/styling-scrollbars/) pseudo element.
 */
export type WebKitScrollbarPseudoElement =
  | "scrollbar"
  | "button"
  | "track"
  | "track-piece"
  | "thumb"
  | "corner"
  | "resizer";
export type ViewTransitionPartName = string;
export type Selector = SelectorComponent[];
export type SelectorList = Selector[];
/**
 * A [keyframe selector](https://drafts.csswg.org/css-animations/#typedef-keyframe-selector) within an `@keyframes` rule.
 */
export type KeyframeSelector =
  | {|
      type: "percentage",
      value: number,
    |}
  | {|
      type: "from",
    |}
  | {|
      type: "to",
    |}
  | {|
      type: "timeline-range-percentage",
      value: TimelineRangePercentage,
    |};
/**
 * KeyframesName
 */
export type KeyframesName =
  | {|
      type: "ident",
      value: String,
    |}
  | {|
      type: "custom",
      value: String,
    |};
/**
 * A property within an `@font-face` rule.
 *
 * See [FontFaceRule](FontFaceRule).
 */
export type FontFaceProperty =
  | {|
      type: "source",
      value: Source[],
    |}
  | {|
      type: "font-family",
      value: FontFamily,
    |}
  | {|
      type: "font-style",
      value: FontStyle2,
    |}
  | {|
      type: "font-weight",
      value: Size2DFor_FontWeight,
    |}
  | {|
      type: "font-stretch",
      value: Size2DFor_FontStretch,
    |}
  | {|
      type: "unicode-range",
      value: UnicodeRange[],
    |}
  | {|
      type: "custom",
      value: CustomProperty,
    |};
/**
 * A value for the [src](https://drafts.csswg.org/css-fonts/#src-desc) property in an `@font-face` rule.
 */
export type Source =
  | {|
      type: "url",
      value: UrlSource,
    |}
  | {|
      type: "local",
      value: FontFamily,
    |};
/**
 * A font format keyword in the `format()` function of the the [src](https://drafts.csswg.org/css-fonts/#src-desc) property of an `@font-face` rule.
 */
export type FontFormat =
  | {|
      type: "woff",
    |}
  | {|
      type: "woff2",
    |}
  | {|
      type: "truetype",
    |}
  | {|
      type: "opentype",
    |}
  | {|
      type: "embedded-opentype",
    |}
  | {|
      type: "collection",
    |}
  | {|
      type: "svg",
    |}
  | {|
      type: "string",
      value: String,
    |};
/**
 * A font format keyword in the `format()` function of the the [src](https://drafts.csswg.org/css-fonts/#src-desc) property of an `@font-face` rule.
 */
export type FontTechnology =
  | "features-opentype"
  | "features-aat"
  | "features-graphite"
  | "color-colrv0"
  | "color-colrv1"
  | "color-svg"
  | "color-sbix"
  | "color-cbdt"
  | "variations"
  | "palettes"
  | "incremental";
/**
 * A value for the [font-style](https://w3c.github.io/csswg-drafts/css-fonts/#descdef-font-face-font-style) descriptor in an `@font-face` rule.
 */
export type FontStyle2 =
  | {|
      type: "normal",
    |}
  | {|
      type: "italic",
    |}
  | {|
      type: "oblique",
      value: Size2DFor_Angle,
    |};
/**
 * A generic value that represents a value with two components, e.g. a border radius.
 *
 * When serialized, only a single component will be written if both are equal.
 * @minItems 2
 * @maxItems 2
 */
export type Size2DFor_Angle = [Angle, Angle];
/**
 * A generic value that represents a value with two components, e.g. a border radius.
 *
 * When serialized, only a single component will be written if both are equal.
 * @minItems 2
 * @maxItems 2
 */
export type Size2DFor_FontWeight = [FontWeight, FontWeight];
/**
 * A generic value that represents a value with two components, e.g. a border radius.
 *
 * When serialized, only a single component will be written if both are equal.
 * @minItems 2
 * @maxItems 2
 */
export type Size2DFor_FontStretch = [FontStretch, FontStretch];
/**
 * A property within an `@font-palette-values` rule.
 *
 * See [FontPaletteValuesRule](FontPaletteValuesRule).
 */
export type FontPaletteValuesProperty =
  | {|
      type: "font-family",
      value: FontFamily,
    |}
  | {|
      type: "base-palette",
      value: BasePalette,
    |}
  | {|
      type: "override-colors",
      value: OverrideColors[],
    |}
  | {|
      type: "custom",
      value: CustomProperty,
    |};
/**
 * A value for the [base-palette](https://drafts.csswg.org/css-fonts-4/#base-palette-desc) property in an `@font-palette-values` rule.
 */
export type BasePalette =
  | {|
      type: "light",
    |}
  | {|
      type: "dark",
    |}
  | {|
      type: "integer",
      value: number,
    |};
/**
 * The name of the `@font-feature-values` sub-rule. font-feature-value-type = <@stylistic> | <@historical-forms> | <@styleset> | <@character-variant> | <@swash> | <@ornaments> | <@annotation>
 */
export type FontFeatureSubruleType =
  | "stylistic"
  | "historical-forms"
  | "styleset"
  | "character-variant"
  | "swash"
  | "ornaments"
  | "annotation";
/**
 * A [page margin box](https://www.w3.org/TR/css-page-3/#margin-boxes).
 */
export type PageMarginBox =
  | "top-left-corner"
  | "top-left"
  | "top-center"
  | "top-right"
  | "top-right-corner"
  | "left-top"
  | "left-middle"
  | "left-bottom"
  | "right-top"
  | "right-middle"
  | "right-bottom"
  | "bottom-left-corner"
  | "bottom-left"
  | "bottom-center"
  | "bottom-right"
  | "bottom-right-corner";
/**
 * A page pseudo class within an `@page` selector.
 *
 * See [PageSelector](PageSelector).
 */
export type PagePseudoClass = "left" | "right" | "first" | "last" | "blank";
/**
 * A parsed value for a [SyntaxComponent](SyntaxComponent).
 */
export type ParsedComponent =
  | {|
      type: "length",
      value: Length,
    |}
  | {|
      type: "number",
      value: number,
    |}
  | {|
      type: "percentage",
      value: number,
    |}
  | {|
      type: "length-percentage",
      value: DimensionPercentageFor_LengthValue,
    |}
  | {|
      type: "color",
      value: CssColor,
    |}
  | {|
      type: "image",
      value: Image,
    |}
  | {|
      type: "url",
      value: Url,
    |}
  | {|
      type: "integer",
      value: number,
    |}
  | {|
      type: "angle",
      value: Angle,
    |}
  | {|
      type: "time",
      value: Time,
    |}
  | {|
      type: "resolution",
      value: Resolution,
    |}
  | {|
      type: "transform-function",
      value: Transform,
    |}
  | {|
      type: "transform-list",
      value: Transform[],
    |}
  | {|
      type: "custom-ident",
      value: String,
    |}
  | {|
      type: "literal",
      value: String,
    |}
  | {|
      type: "repeated",
      value: {|
        /**
         * The components to repeat.
         */
        components: ParsedComponent[],

        /**
         * A multiplier describing how the components repeat.
         */
        multiplier: Multiplier,
      |},
    |}
  | {|
      type: "token-list",
      value: TokenOrValue[],
    |};
/**
 * A [multiplier](https://drafts.css-houdini.org/css-properties-values-api/#multipliers) for a [SyntaxComponent](SyntaxComponent). Indicates whether and how the component may be repeated.
 */
export type Multiplier =
  | {|
      type: "none",
    |}
  | {|
      type: "space",
    |}
  | {|
      type: "comma",
    |};
/**
 * A CSS [syntax string](https://drafts.css-houdini.org/css-properties-values-api/#syntax-strings) used to define the grammar for a registered custom property.
 */
export type SyntaxString =
  | {|
      type: "components",
      value: SyntaxComponent[],
    |}
  | {|
      type: "universal",
    |};
/**
 * A [syntax component component name](https://drafts.css-houdini.org/css-properties-values-api/#supported-names).
 */
export type SyntaxComponentKind =
  | {|
      type: "length",
    |}
  | {|
      type: "number",
    |}
  | {|
      type: "percentage",
    |}
  | {|
      type: "length-percentage",
    |}
  | {|
      type: "color",
    |}
  | {|
      type: "image",
    |}
  | {|
      type: "url",
    |}
  | {|
      type: "integer",
    |}
  | {|
      type: "angle",
    |}
  | {|
      type: "time",
    |}
  | {|
      type: "resolution",
    |}
  | {|
      type: "transform-function",
    |}
  | {|
      type: "transform-list",
    |}
  | {|
      type: "custom-ident",
    |}
  | {|
      type: "literal",
      value: string,
    |};
/**
 * Represents a container condition.
 */
export type ContainerCondition<D = Declaration> =
  | {|
      type: "feature",
      value: QueryFeatureFor_ContainerSizeFeatureId,
    |}
  | {|
      type: "not",
      value: ContainerCondition<D>,
    |}
  | {|
      /**
       * The conditions for the operator.
       */
      conditions: ContainerCondition<D>[],

      /**
       * The operator for the conditions.
       */
      operator: Operator,
      type: "operation",
    |}
  | {|
      type: "style",
      value: StyleQuery<D>,
    |};
/**
 * A generic media feature or container feature.
 */
export type QueryFeatureFor_ContainerSizeFeatureId =
  | {|
      /**
       * The name of the feature.
       */
      name: MediaFeatureNameFor_ContainerSizeFeatureId,
      type: "plain",

      /**
       * The feature value.
       */
      value: MediaFeatureValue,
    |}
  | {|
      /**
       * The name of the feature.
       */
      name: MediaFeatureNameFor_ContainerSizeFeatureId,
      type: "boolean",
    |}
  | {|
      /**
       * The name of the feature.
       */
      name: MediaFeatureNameFor_ContainerSizeFeatureId,

      /**
       * A comparator.
       */
      operator: MediaFeatureComparison,
      type: "range",

      /**
       * The feature value.
       */
      value: MediaFeatureValue,
    |}
  | {|
      /**
       * The end value.
       */
      end: MediaFeatureValue,

      /**
       * A comparator for the end value.
       */
      endOperator: MediaFeatureComparison,

      /**
       * The name of the feature.
       */
      name: MediaFeatureNameFor_ContainerSizeFeatureId,

      /**
       * A start value.
       */
      start: MediaFeatureValue,

      /**
       * A comparator for the start value.
       */
      startOperator: MediaFeatureComparison,
      type: "interval",
    |};
/**
 * A media feature name.
 */
export type MediaFeatureNameFor_ContainerSizeFeatureId =
  | ContainerSizeFeatureId
  | String
  | String;
/**
 * A container query size feature identifier.
 */
export type ContainerSizeFeatureId =
  | "width"
  | "height"
  | "inline-size"
  | "block-size"
  | "aspect-ratio"
  | "orientation";
/**
 * Represents a style query within a container condition.
 */
export type StyleQuery<D = Declaration> =
  | {|
      type: "declaration",
      value: D,
    |}
  | {|
      type: "property",
      value: PropertyId,
    |}
  | {|
      type: "not",
      value: StyleQuery<D>,
    |}
  | {|
      /**
       * The conditions for the operator.
       */
      conditions: StyleQuery<D>[],

      /**
       * The operator for the conditions.
       */
      operator: Operator,
      type: "operation",
    |};
/**
 * A property within a `@view-transition` rule.
 *
 * See [ViewTransitionRule](ViewTransitionRule).
 */
export type ViewTransitionProperty =
  | {|
      property: "navigation",
      value: Navigation,
    |}
  | {|
      property: "types",
      value: NoneOrCustomIdentList,
    |}
  | {|
      property: "custom",
      value: CustomProperty,
    |};
/**
 * A value for the [navigation](https://drafts.csswg.org/css-view-transitions-2/#view-transition-navigation-descriptor) property in a `@view-transition` rule.
 */
export type Navigation = "none" | "auto";
export type DefaultAtRule = null;
/**
 * A CSS style sheet, representing a `.css` file or inline `<style>` element.
 *
 * Style sheets can be parsed from a string, constructed from scratch, or created using a [Bundler](super::bundler::Bundler). Then, they can be minified and transformed for a set of target browsers, and serialied to a string.
 *
 * # Example
 *
 * ``` use lightningcss::stylesheet::{ StyleSheet, ParserOptions, MinifyOptions, PrinterOptions };
 *
 * // Parse a style sheet from a string. let mut stylesheet = StyleSheet::parse( r#" .foo { color: red; }
 *
 * .bar { color: red; } "#, ParserOptions::default() ).unwrap();
 *
 * // Minify the stylesheet. stylesheet.minify(MinifyOptions::default()).unwrap();
 *
 * // Serialize it to a string. let res = stylesheet.to_css(PrinterOptions::default()).unwrap(); assert_eq!(res.code, ".foo, .bar {\n  color: red;\n}\n"); ```
 */
export interface StyleSheet<D = Declaration, M = MediaQuery> {
  /**
   * The license comments that appeared at the start of the file.
   */
  licenseComments: String[];

  /**
   * A list of top-level rules within the style sheet.
   */
  rules: Rule<D, M>[];

  /**
   * The source map URL extracted from the original style sheet.
   */
  sourceMapUrls: (string | null)[];

  /**
   * A list of file names for all source files included within the style sheet. Sources are referenced by index in the `loc` property of each rule.
   */
  sources: string[];
}
/**
 * A [@media](https://drafts.csswg.org/css-conditional-3/#at-media) rule.
 */
export interface MediaRule<D = Declaration, M = MediaQuery> {
  /**
   * The location of the rule in the source file.
   */
  loc: Location2;

  /**
   * The media query list.
   */
  query: MediaList<M>;

  /**
   * The rules within the `@media` rule.
   */
  rules: Rule<D, M>[];
}
/**
 * A source location.
 */
export interface Location2 {
  /**
   * The column number within a line, starting at 1 for first the character of the line. Column numbers are counted in UTF-16 code units.
   */
  column: number;

  /**
   * The line number, starting at 0.
   */
  line: number;

  /**
   * The index of the source file within the source map.
   */
  source_index: number;
}
/**
 * A [media query list](https://drafts.csswg.org/mediaqueries/#mq-list).
 */
export interface MediaList<M = MediaQuery> {
  /**
   * The list of media queries.
   */
  mediaQueries: M[];
}
/**
 * A [media query](https://drafts.csswg.org/mediaqueries/#media).
 */
export interface MediaQuery {
  /**
   * The condition that this media query contains. This cannot have `or` in the first level.
   */
  condition?: MediaCondition | null;

  /**
   * The media type for this query, that can be known, unknown, or "all".
   */
  mediaType: MediaType;

  /**
   * The qualifier for this query.
   */
  qualifier?: Qualifier | null;
}
export interface LengthValue {
  /**
   * The length unit.
   */
  unit: LengthUnit;

  /**
   * The length value.
   */
  value: number;
}
/**
 * A CSS environment variable reference.
 */
export interface EnvironmentVariable {
  /**
   * A fallback value in case the variable is not defined.
   */
  fallback?: TokenOrValue[] | null;

  /**
   * Optional indices into the dimensions of the environment variable.
   */
  indices?: number[];

  /**
   * The environment variable name.
   */
  name: EnvironmentVariableName;
}
/**
 * A CSS [url()](https://www.w3.org/TR/css-values-4/#urls) value and its source location.
 */
export interface Url {
  /**
   * The location where the `url()` was seen in the CSS source file.
   */
  loc: Location;

  /**
   * The url string.
   */
  url: String;
}
/**
 * A line and column position within a source file.
 */
export interface Location {
  /**
   * The column number, starting from 1.
   */
  column: number;

  /**
   * The line number, starting from 1.
   */
  line: number;
}
/**
 * A CSS variable reference.
 */
export interface Variable {
  /**
   * A fallback value in case the variable is not defined.
   */
  fallback?: TokenOrValue[] | null;

  /**
   * The variable name.
   */
  name: DashedIdentReference;
}
/**
 * A CSS [`<dashed-ident>`](https://www.w3.org/TR/css-values-4/#dashed-idents) reference.
 *
 * Dashed idents are used in cases where an identifier can be either author defined _or_ CSS-defined. Author defined idents must start with two dash characters ("--") or parsing will fail.
 *
 * In CSS modules, when the `dashed_idents` option is enabled, the identifier may be followed by the `from` keyword and an argument indicating where the referenced identifier is declared (e.g. a filename).
 */
export interface DashedIdentReference {
  /**
   * CSS modules extension: the filename where the variable is defined. Only enabled when the CSS modules `dashed_idents` option is turned on.
   */
  from?: Specifier | null;

  /**
   * The referenced identifier.
   */
  ident: String;
}
/**
 * A custom CSS function.
 */
export interface Function {
  /**
   * The function arguments.
   */
  arguments: TokenOrValue[];

  /**
   * The function name.
   */
  name: String;
}
/**
 * A [@import](https://drafts.csswg.org/css-cascade/#at-import) rule.
 */
export interface ImportRule<M = MediaQuery> {
  /**
   * An optional cascade layer name, or `None` for an anonymous layer.
   */
  layer?: String[] | null;

  /**
   * The location of the rule in the source file.
   */
  loc: Location2;

  /**
   * A media query.
   */
  media?: MediaList<M>;

  /**
   * An optional `supports()` condition.
   */
  supports?: SupportsCondition | null;

  /**
   * The url to import.
   */
  url: String;
}
/**
 * A CSS [style rule](https://drafts.csswg.org/css-syntax/#style-rules).
 */
export interface StyleRule<D = Declaration, M = MediaQuery> {
  /**
   * The declarations within the style rule.
   */
  declarations?: DeclarationBlock<D>;

  /**
   * The location of the rule in the source file.
   */
  loc: Location2;

  /**
   * Nested rules within the style rule.
   */
  rules?: Rule<D, M>[];

  /**
   * The selectors for the style rule.
   */
  selectors: SelectorList;
}
/**
 * A CSS declaration block.
 *
 * Properties are separated into a list of `!important` declararations, and a list of normal declarations. This reduces memory usage compared with storing a boolean along with each property.
 */
export interface DeclarationBlock<D = Declaration> {
  /**
   * A list of normal declarations in the block.
   */
  declarations?: D[];

  /**
   * A list of `!important` declarations in the block.
   */
  importantDeclarations?: D[];
}
/**
 * A CSS [`<position>`](https://www.w3.org/TR/css3-values/#position) value, as used in the `background-position` property, gradients, masks, etc.
 */
export interface Position {
  /**
   * The x-position.
   */
  x: PositionComponentFor_HorizontalPositionKeyword;

  /**
   * The y-position.
   */
  y: PositionComponentFor_VerticalPositionKeyword;
}
/**
 * An x/y position within a legacy `-webkit-gradient()`.
 */
export interface WebKitGradientPoint {
  /**
   * The x-position.
   */
  x: WebKitGradientPointComponentFor_HorizontalPositionKeyword;

  /**
   * The y-position.
   */
  y: WebKitGradientPointComponentFor_VerticalPositionKeyword;
}
/**
 * A color stop within a legacy `-webkit-gradient()`.
 */
export interface WebKitColorStop {
  /**
   * The color of the color stop.
   */
  color: CssColor;

  /**
   * The position of the color stop.
   */
  position: number;
}
/**
 * A CSS [`image-set()`](https://drafts.csswg.org/css-images-4/#image-set-notation) value.
 *
 * `image-set()` allows the user agent to choose between multiple versions of an image to display the most appropriate resolution or file type that it supports.
 */
export interface ImageSet {
  /**
   * The image options to choose from.
   */
  options: ImageSetOption[];

  /**
   * The vendor prefix for the `image-set()` function.
   */
  vendorPrefix: VendorPrefix;
}
/**
 * An image option within the `image-set()` function. See [ImageSet](ImageSet).
 */
export interface ImageSetOption {
  /**
   * The mime type of the image.
   */
  fileType?: String | null;

  /**
   * The image for this option.
   */
  image: Image;

  /**
   * The resolution of the image.
   */
  resolution: Resolution;
}
/**
 * A value for the [background-position](https://drafts.csswg.org/css-backgrounds/#background-position) shorthand property.
 */
export interface BackgroundPosition {
  /**
   * The x-position.
   */
  x: PositionComponentFor_HorizontalPositionKeyword;

  /**
   * The y-position.
   */
  y: PositionComponentFor_VerticalPositionKeyword;
}
/**
 * A value for the [background-repeat](https://www.w3.org/TR/css-backgrounds-3/#background-repeat) property.
 */
export interface BackgroundRepeat {
  /**
   * A repeat style for the x direction.
   */
  x: BackgroundRepeatKeyword;

  /**
   * A repeat style for the y direction.
   */
  y: BackgroundRepeatKeyword;
}
/**
 * A value for the [background](https://www.w3.org/TR/css-backgrounds-3/#background) shorthand property.
 */
export interface Background {
  /**
   * The background attachment.
   */
  attachment: BackgroundAttachment;

  /**
   * How the background should be clipped.
   */
  clip: BackgroundClip;

  /**
   * The background color.
   */
  color: CssColor;

  /**
   * The background image.
   */
  image: Image;

  /**
   * The background origin.
   */
  origin: BackgroundOrigin;

  /**
   * The background position.
   */
  position: BackgroundPosition;

  /**
   * How the background image should repeat.
   */
  repeat: BackgroundRepeat;

  /**
   * The size of the background image.
   */
  size: BackgroundSize;
}
/**
 * A value for the [box-shadow](https://drafts.csswg.org/css-backgrounds/#box-shadow) property.
 */
export interface BoxShadow {
  /**
   * The blur radius of the shadow.
   */
  blur: Length;

  /**
   * The color of the box shadow.
   */
  color: CssColor;

  /**
   * Whether the shadow is inset within the box.
   */
  inset: boolean;

  /**
   * The spread distance of the shadow.
   */
  spread: Length;

  /**
   * The x offset of the shadow.
   */
  xOffset: Length;

  /**
   * The y offset of the shadow.
   */
  yOffset: Length;
}
/**
 * A value for the [aspect-ratio](https://drafts.csswg.org/css-sizing-4/#aspect-ratio) property.
 */
export interface AspectRatio {
  /**
   * The `auto` keyword.
   */
  auto: boolean;

  /**
   * A preferred aspect ratio for the box, specified as width / height.
   */
  ratio?: Ratio | null;
}
/**
 * A value for the [overflow](https://www.w3.org/TR/css-overflow-3/#overflow-properties) shorthand property.
 */
export interface Overflow {
  /**
   * The overflow mode for the x direction.
   */
  x: OverflowKeyword;

  /**
   * The overflow mode for the y direction.
   */
  y: OverflowKeyword;
}
/**
 * A value for the [inset-block](https://drafts.csswg.org/css-logical/#propdef-inset-block) shorthand property.
 */
export interface InsetBlock {
  /**
   * The block end value.
   */
  blockEnd: LengthPercentageOrAuto;

  /**
   * The block start value.
   */
  blockStart: LengthPercentageOrAuto;
}
/**
 * A value for the [inset-inline](https://drafts.csswg.org/css-logical/#propdef-inset-inline) shorthand property.
 */
export interface InsetInline {
  /**
   * The inline end value.
   */
  inlineEnd: LengthPercentageOrAuto;

  /**
   * The inline start value.
   */
  inlineStart: LengthPercentageOrAuto;
}
/**
 * A value for the [inset](https://drafts.csswg.org/css-logical/#propdef-inset) shorthand property.
 */
export interface Inset {
  /**
   * The bottom value.
   */
  bottom: LengthPercentageOrAuto;

  /**
   * The left value.
   */
  left: LengthPercentageOrAuto;

  /**
   * The right value.
   */
  right: LengthPercentageOrAuto;

  /**
   * The top value.
   */
  top: LengthPercentageOrAuto;
}
/**
 * A value for the [border-radius](https://www.w3.org/TR/css-backgrounds-3/#border-radius) property.
 */
export interface BorderRadius {
  /**
   * The x and y radius values for the bottom left corner.
   */
  bottomLeft: Size2DFor_DimensionPercentageFor_LengthValue;

  /**
   * The x and y radius values for the bottom right corner.
   */
  bottomRight: Size2DFor_DimensionPercentageFor_LengthValue;

  /**
   * The x and y radius values for the top left corner.
   */
  topLeft: Size2DFor_DimensionPercentageFor_LengthValue;

  /**
   * The x and y radius values for the top right corner.
   */
  topRight: Size2DFor_DimensionPercentageFor_LengthValue;
}
/**
 * A value for the [border-image-repeat](https://www.w3.org/TR/css-backgrounds-3/#border-image-repeat) property.
 */
export interface BorderImageRepeat {
  /**
   * The horizontal repeat value.
   */
  horizontal: BorderImageRepeatKeyword;

  /**
   * The vertical repeat value.
   */
  vertical: BorderImageRepeatKeyword;
}
/**
 * A value for the [border-image-slice](https://www.w3.org/TR/css-backgrounds-3/#border-image-slice) property.
 */
export interface BorderImageSlice {
  /**
   * Whether the middle of the border image should be preserved.
   */
  fill: boolean;

  /**
   * The offsets from the edges of the image.
   */
  offsets: RectFor_NumberOrPercentage;
}
/**
 * A value for the [border-image](https://www.w3.org/TR/css-backgrounds-3/#border-image) shorthand property.
 */
export interface BorderImage {
  /**
   * The amount that the image extends beyond the border box.
   */
  outset: RectFor_LengthOrNumber;

  /**
   * How the border image is scaled and tiled.
   */
  repeat: BorderImageRepeat;

  /**
   * The offsets that define where the image is sliced.
   */
  slice: BorderImageSlice;

  /**
   * The border image.
   */
  source: Image;

  /**
   * The width of the border image.
   */
  width: RectFor_BorderImageSideWidth;
}
/**
 * A value for the [border-color](https://drafts.csswg.org/css-backgrounds/#propdef-border-color) shorthand property.
 */
export interface BorderColor {
  /**
   * The bottom value.
   */
  bottom: CssColor;

  /**
   * The left value.
   */
  left: CssColor;

  /**
   * The right value.
   */
  right: CssColor;

  /**
   * The top value.
   */
  top: CssColor;
}
/**
 * A value for the [border-style](https://drafts.csswg.org/css-backgrounds/#propdef-border-style) shorthand property.
 */
export interface BorderStyle {
  /**
   * The bottom value.
   */
  bottom: LineStyle;

  /**
   * The left value.
   */
  left: LineStyle;

  /**
   * The right value.
   */
  right: LineStyle;

  /**
   * The top value.
   */
  top: LineStyle;
}
/**
 * A value for the [border-width](https://drafts.csswg.org/css-backgrounds/#propdef-border-width) shorthand property.
 */
export interface BorderWidth {
  /**
   * The bottom value.
   */
  bottom: BorderSideWidth;

  /**
   * The left value.
   */
  left: BorderSideWidth;

  /**
   * The right value.
   */
  right: BorderSideWidth;

  /**
   * The top value.
   */
  top: BorderSideWidth;
}
/**
 * A value for the [border-block-color](https://drafts.csswg.org/css-logical/#propdef-border-block-color) shorthand property.
 */
export interface BorderBlockColor {
  /**
   * The block end value.
   */
  end: CssColor;

  /**
   * The block start value.
   */
  start: CssColor;
}
/**
 * A value for the [border-block-style](https://drafts.csswg.org/css-logical/#propdef-border-block-style) shorthand property.
 */
export interface BorderBlockStyle {
  /**
   * The block end value.
   */
  end: LineStyle;

  /**
   * The block start value.
   */
  start: LineStyle;
}
/**
 * A value for the [border-block-width](https://drafts.csswg.org/css-logical/#propdef-border-block-width) shorthand property.
 */
export interface BorderBlockWidth {
  /**
   * The block end value.
   */
  end: BorderSideWidth;

  /**
   * The block start value.
   */
  start: BorderSideWidth;
}
/**
 * A value for the [border-inline-color](https://drafts.csswg.org/css-logical/#propdef-border-inline-color) shorthand property.
 */
export interface BorderInlineColor {
  /**
   * The inline end value.
   */
  end: CssColor;

  /**
   * The inline start value.
   */
  start: CssColor;
}
/**
 * A value for the [border-inline-style](https://drafts.csswg.org/css-logical/#propdef-border-inline-style) shorthand property.
 */
export interface BorderInlineStyle {
  /**
   * The inline end value.
   */
  end: LineStyle;

  /**
   * The inline start value.
   */
  start: LineStyle;
}
/**
 * A value for the [border-inline-width](https://drafts.csswg.org/css-logical/#propdef-border-inline-width) shorthand property.
 */
export interface BorderInlineWidth {
  /**
   * The inline end value.
   */
  end: BorderSideWidth;

  /**
   * The inline start value.
   */
  start: BorderSideWidth;
}
/**
 * A generic type that represents the `border` and `outline` shorthand properties.
 */
export interface GenericBorderFor_LineStyle {
  /**
   * The border color.
   */
  color: CssColor;

  /**
   * The border style.
   */
  style: LineStyle;

  /**
   * The width of the border.
   */
  width: BorderSideWidth;
}
/**
 * A generic type that represents the `border` and `outline` shorthand properties.
 */
export interface GenericBorderFor_OutlineStyleAnd_11 {
  /**
   * The border color.
   */
  color: CssColor;

  /**
   * The border style.
   */
  style: OutlineStyle;

  /**
   * The width of the border.
   */
  width: BorderSideWidth;
}
/**
 * A value for the [flex-flow](https://www.w3.org/TR/2018/CR-css-flexbox-1-20181119/#flex-flow-property) shorthand property.
 */
export interface FlexFlow {
  /**
   * The direction that flex items flow.
   */
  direction: FlexDirection;

  /**
   * How the flex items wrap.
   */
  wrap: FlexWrap;
}
/**
 * A value for the [flex](https://www.w3.org/TR/2018/CR-css-flexbox-1-20181119/#flex-property) shorthand property.
 */
export interface Flex {
  /**
   * The flex basis.
   */
  basis: LengthPercentageOrAuto;

  /**
   * The flex grow factor.
   */
  grow: number;

  /**
   * The flex shrink factor.
   */
  shrink: number;
}
/**
 * A value for the [place-content](https://www.w3.org/TR/css-align-3/#place-content) shorthand property.
 */
export interface PlaceContent {
  /**
   * The content alignment.
   */
  align: AlignContent;

  /**
   * The content justification.
   */
  justify: JustifyContent;
}
/**
 * A value for the [place-self](https://www.w3.org/TR/css-align-3/#place-self-property) shorthand property.
 */
export interface PlaceSelf {
  /**
   * The item alignment.
   */
  align: AlignSelf;

  /**
   * The item justification.
   */
  justify: JustifySelf;
}
/**
 * A value for the [place-items](https://www.w3.org/TR/css-align-3/#place-items-property) shorthand property.
 */
export interface PlaceItems {
  /**
   * The item alignment.
   */
  align: AlignItems;

  /**
   * The item justification.
   */
  justify: JustifyItems;
}
/**
 * A value for the [gap](https://www.w3.org/TR/css-align-3/#gap-shorthand) shorthand property.
 */
export interface Gap {
  /**
   * The column gap.
   */
  column: GapValue;

  /**
   * The row gap.
   */
  row: GapValue;
}
/**
 * A [`<track-repeat>`](https://drafts.csswg.org/css-grid-2/#typedef-track-repeat) value, representing the `repeat()` function in a track list.
 *
 * See [TrackListItem](TrackListItem).
 */
export interface TrackRepeat {
  /**
   * The repeat count.
   */
  count: RepeatCount;

  /**
   * The line names to repeat.
   */
  lineNames: String[][];

  /**
   * The track sizes to repeat.
   */
  trackSizes: TrackSize[];
}
export interface GridAutoFlow {
  /**
   * If specified, a dense packing algorithm is used, which fills in holes in the grid.
   */
  dense: boolean;

  /**
   * The direction of the auto flow.
   */
  direction: AutoFlowDirection;
}
/**
 * A value for the [grid-template](https://drafts.csswg.org/css-grid-2/#explicit-grid-shorthand) shorthand property.
 *
 * If `areas` is not `None`, then `rows` must also not be `None`.
 */
export interface GridTemplate {
  /**
   * The named grid areas.
   */
  areas: GridTemplateAreas;

  /**
   * The grid template columns.
   */
  columns: TrackSizing;

  /**
   * The grid template rows.
   */
  rows: TrackSizing;
}
/**
 * A value for the [grid](https://drafts.csswg.org/css-grid-2/#grid-shorthand) shorthand property.
 *
 * Explicit and implicit values may not be combined.
 */
export interface Grid {
  /**
   * Explicit grid template areas.
   */
  areas: GridTemplateAreas;

  /**
   * The grid auto columns.
   */
  autoColumns: TrackSize[];

  /**
   * The grid auto flow.
   */
  autoFlow: GridAutoFlow;

  /**
   * The grid auto rows.
   */
  autoRows: TrackSize[];

  /**
   * Explicit grid template columns.
   */
  columns: TrackSizing;

  /**
   * Explicit grid template rows.
   */
  rows: TrackSizing;
}
/**
 * A value for the [grid-row](https://drafts.csswg.org/css-grid-2/#propdef-grid-row) shorthand property.
 */
export interface GridRow {
  /**
   * The ending line.
   */
  end: GridLine;

  /**
   * The starting line.
   */
  start: GridLine;
}
/**
 * A value for the [grid-row](https://drafts.csswg.org/css-grid-2/#propdef-grid-column) shorthand property.
 */
export interface GridColumn {
  /**
   * The ending line.
   */
  end: GridLine;

  /**
   * The starting line.
   */
  start: GridLine;
}
/**
 * A value for the [grid-area](https://drafts.csswg.org/css-grid-2/#propdef-grid-area) shorthand property.
 */
export interface GridArea {
  /**
   * The grid column end placement.
   */
  columnEnd: GridLine;

  /**
   * The grid column start placement.
   */
  columnStart: GridLine;

  /**
   * The grid row end placement.
   */
  rowEnd: GridLine;

  /**
   * The grid row start placement.
   */
  rowStart: GridLine;
}
/**
 * A value for the [margin-block](https://drafts.csswg.org/css-logical/#propdef-margin-block) shorthand property.
 */
export interface MarginBlock {
  /**
   * The block end value.
   */
  blockEnd: LengthPercentageOrAuto;

  /**
   * The block start value.
   */
  blockStart: LengthPercentageOrAuto;
}
/**
 * A value for the [margin-inline](https://drafts.csswg.org/css-logical/#propdef-margin-inline) shorthand property.
 */
export interface MarginInline {
  /**
   * The inline end value.
   */
  inlineEnd: LengthPercentageOrAuto;

  /**
   * The inline start value.
   */
  inlineStart: LengthPercentageOrAuto;
}
/**
 * A value for the [margin](https://drafts.csswg.org/css-box-4/#propdef-margin) shorthand property.
 */
export interface Margin {
  /**
   * The bottom value.
   */
  bottom: LengthPercentageOrAuto;

  /**
   * The left value.
   */
  left: LengthPercentageOrAuto;

  /**
   * The right value.
   */
  right: LengthPercentageOrAuto;

  /**
   * The top value.
   */
  top: LengthPercentageOrAuto;
}
/**
 * A value for the [padding-block](https://drafts.csswg.org/css-logical/#propdef-padding-block) shorthand property.
 */
export interface PaddingBlock {
  /**
   * The block end value.
   */
  blockEnd: LengthPercentageOrAuto;

  /**
   * The block start value.
   */
  blockStart: LengthPercentageOrAuto;
}
/**
 * A value for the [padding-inline](https://drafts.csswg.org/css-logical/#propdef-padding-inline) shorthand property.
 */
export interface PaddingInline {
  /**
   * The inline end value.
   */
  inlineEnd: LengthPercentageOrAuto;

  /**
   * The inline start value.
   */
  inlineStart: LengthPercentageOrAuto;
}
/**
 * A value for the [padding](https://drafts.csswg.org/css-box-4/#propdef-padding) shorthand property.
 */
export interface Padding {
  /**
   * The bottom value.
   */
  bottom: LengthPercentageOrAuto;

  /**
   * The left value.
   */
  left: LengthPercentageOrAuto;

  /**
   * The right value.
   */
  right: LengthPercentageOrAuto;

  /**
   * The top value.
   */
  top: LengthPercentageOrAuto;
}
/**
 * A value for the [scroll-margin-block](https://drafts.csswg.org/css-scroll-snap/#propdef-scroll-margin-block) shorthand property.
 */
export interface ScrollMarginBlock {
  /**
   * The block end value.
   */
  blockEnd: LengthPercentageOrAuto;

  /**
   * The block start value.
   */
  blockStart: LengthPercentageOrAuto;
}
/**
 * A value for the [scroll-margin-inline](https://drafts.csswg.org/css-scroll-snap/#propdef-scroll-margin-inline) shorthand property.
 */
export interface ScrollMarginInline {
  /**
   * The inline end value.
   */
  inlineEnd: LengthPercentageOrAuto;

  /**
   * The inline start value.
   */
  inlineStart: LengthPercentageOrAuto;
}
/**
 * A value for the [scroll-margin](https://drafts.csswg.org/css-scroll-snap/#scroll-margin) shorthand property.
 */
export interface ScrollMargin {
  /**
   * The bottom value.
   */
  bottom: LengthPercentageOrAuto;

  /**
   * The left value.
   */
  left: LengthPercentageOrAuto;

  /**
   * The right value.
   */
  right: LengthPercentageOrAuto;

  /**
   * The top value.
   */
  top: LengthPercentageOrAuto;
}
/**
 * A value for the [scroll-padding-block](https://drafts.csswg.org/css-scroll-snap/#propdef-scroll-padding-block) shorthand property.
 */
export interface ScrollPaddingBlock {
  /**
   * The block end value.
   */
  blockEnd: LengthPercentageOrAuto;

  /**
   * The block start value.
   */
  blockStart: LengthPercentageOrAuto;
}
/**
 * A value for the [scroll-padding-inline](https://drafts.csswg.org/css-scroll-snap/#propdef-scroll-padding-inline) shorthand property.
 */
export interface ScrollPaddingInline {
  /**
   * The inline end value.
   */
  inlineEnd: LengthPercentageOrAuto;

  /**
   * The inline start value.
   */
  inlineStart: LengthPercentageOrAuto;
}
/**
 * A value for the [scroll-padding](https://drafts.csswg.org/css-scroll-snap/#scroll-padding) shorthand property.
 */
export interface ScrollPadding {
  /**
   * The bottom value.
   */
  bottom: LengthPercentageOrAuto;

  /**
   * The left value.
   */
  left: LengthPercentageOrAuto;

  /**
   * The right value.
   */
  right: LengthPercentageOrAuto;

  /**
   * The top value.
   */
  top: LengthPercentageOrAuto;
}
/**
 * A value for the [font](https://www.w3.org/TR/css-fonts-4/#font-prop) shorthand property.
 */
export interface Font {
  /**
   * The font family.
   */
  family: FontFamily[];

  /**
   * The line height.
   */
  lineHeight: LineHeight;

  /**
   * The font size.
   */
  size: FontSize;

  /**
   * The font stretch.
   */
  stretch: FontStretch;

  /**
   * The font style.
   */
  style: FontStyle;

  /**
   * How the text should be capitalized. Only CSS 2.1 values are supported.
   */
  variantCaps: FontVariantCaps;

  /**
   * The font weight.
   */
  weight: FontWeight;
}
/**
 * A value for the [transition](https://www.w3.org/TR/2018/WD-css-transitions-1-20181011/#transition-shorthand-property) property.
 */
export interface Transition {
  /**
   * The delay before the transition starts.
   */
  delay: Time;

  /**
   * The duration of the transition.
   */
  duration: Time;

  /**
   * The property to transition.
   */
  property: PropertyId;

  /**
   * The easing function for the transition.
   */
  timingFunction: EasingFunction;
}
/**
 * The [scroll()](https://drafts.csswg.org/scroll-animations-1/#scroll-notation) function.
 */
export interface ScrollTimeline {
  /**
   * Specifies which axis of the scroll container to use as the progress for the timeline.
   */
  axis: ScrollAxis;

  /**
   * Specifies which element to use as the scroll container.
   */
  scroller: Scroller;
}
/**
 * The [view()](https://drafts.csswg.org/scroll-animations-1/#view-notation) function.
 */
export interface ViewTimeline {
  /**
   * Specifies which axis of the scroll container to use as the progress for the timeline.
   */
  axis: ScrollAxis;

  /**
   * Provides an adjustment of the view progress visibility range.
   */
  inset: Size2DFor_LengthPercentageOrAuto;
}
/**
 * A value for the [animation-range](https://drafts.csswg.org/scroll-animations/#animation-range) shorthand property.
 */
export interface AnimationRange {
  /**
   * The end of the animation's attachment range.
   */
  end: AnimationRangeEnd;

  /**
   * The start of the animation's attachment range.
   */
  start: AnimationRangeStart;
}
/**
 * A value for the [animation](https://drafts.csswg.org/css-animations/#animation) shorthand property.
 */
export interface Animation {
  /**
   * The animation delay.
   */
  delay: Time;

  /**
   * The direction of the animation.
   */
  direction: AnimationDirection;

  /**
   * The animation duration.
   */
  duration: Time;

  /**
   * The animation fill mode.
   */
  fillMode: AnimationFillMode;

  /**
   * The number of times the animation will run.
   */
  iterationCount: AnimationIterationCount;

  /**
   * The animation name.
   */
  name: AnimationName;

  /**
   * The current play state of the animation.
   */
  playState: AnimationPlayState;

  /**
   * The animation timeline.
   */
  timeline: AnimationTimeline;

  /**
   * The easing function for the animation.
   */
  timingFunction: EasingFunction;
}
/**
 * A 2D matrix.
 */
export interface MatrixForFloat {
  a: number;
  b: number;
  c: number;
  d: number;
  e: number;
  f: number;
}
/**
 * A 3D matrix.
 */
export interface Matrix3DForFloat {
  m11: number;
  m12: number;
  m13: number;
  m14: number;
  m21: number;
  m22: number;
  m23: number;
  m24: number;
  m31: number;
  m32: number;
  m33: number;
  m34: number;
  m41: number;
  m42: number;
  m43: number;
  m44: number;
}
/**
 * A value for the [rotate](https://drafts.csswg.org/css-transforms-2/#propdef-rotate) property.
 */
export interface Rotate {
  /**
   * The angle of rotation.
   */
  angle: Angle;

  /**
   * Rotation around the x axis.
   */
  x: number;

  /**
   * Rotation around the y axis.
   */
  y: number;

  /**
   * Rotation around the z axis.
   */
  z: number;
}
/**
 * A value for the [text-transform](https://www.w3.org/TR/2021/CRD-css-text-3-20210422/#text-transform-property) property.
 */
export interface TextTransform {
  /**
   * How case should be transformed.
   */
  case: TextTransformCase;

  /**
   * Converts all small Kana characters to the equivalent full-size Kana.
   */
  fullSizeKana: boolean;

  /**
   * Puts all typographic character units in full-width form.
   */
  fullWidth: boolean;
}
/**
 * A value for the [text-indent](https://www.w3.org/TR/2021/CRD-css-text-3-20210422/#text-indent-property) property.
 */
export interface TextIndent {
  /**
   * Affects the first line after each hard break.
   */
  eachLine: boolean;

  /**
   * Inverts which lines are affected.
   */
  hanging: boolean;

  /**
   * The amount to indent.
   */
  value: DimensionPercentageFor_LengthValue;
}
/**
 * A value for the [text-decoration](https://www.w3.org/TR/2020/WD-css-text-decor-4-20200506/#text-decoration-property) shorthand property.
 */
export interface TextDecoration {
  /**
   * The color of the lines.
   */
  color: CssColor;

  /**
   * The lines to display.
   */
  line: TextDecorationLine;

  /**
   * The style of the lines.
   */
  style: TextDecorationStyle;

  /**
   * The thickness of the lines.
   */
  thickness: TextDecorationThickness;
}
/**
 * A value for the [text-emphasis](https://www.w3.org/TR/2020/WD-css-text-decor-4-20200506/#text-emphasis-property) shorthand property.
 */
export interface TextEmphasis {
  /**
   * The text emphasis color.
   */
  color: CssColor;

  /**
   * The text emphasis style.
   */
  style: TextEmphasisStyle;
}
/**
 * A value for the [text-emphasis-position](https://www.w3.org/TR/2020/WD-css-text-decor-4-20200506/#text-emphasis-position-property) property.
 */
export interface TextEmphasisPosition {
  /**
   * The horizontal position.
   */
  horizontal: TextEmphasisPositionHorizontal;

  /**
   * The vertical position.
   */
  vertical: TextEmphasisPositionVertical;
}
/**
 * A value for the [text-shadow](https://www.w3.org/TR/2020/WD-css-text-decor-4-20200506/#text-shadow-property) property.
 */
export interface TextShadow {
  /**
   * The blur radius of the text shadow.
   */
  blur: Length;

  /**
   * The color of the text shadow.
   */
  color: CssColor;

  /**
   * The spread distance of the text shadow.
   */
  spread: Length;

  /**
   * The x offset of the text shadow.
   */
  xOffset: Length;

  /**
   * The y offset of the text shadow.
   */
  yOffset: Length;
}
/**
 * A value for the [cursor](https://www.w3.org/TR/2021/WD-css-ui-4-20210316/#cursor) property.
 */
export interface Cursor {
  /**
   * A list of cursor images.
   */
  images: CursorImage[];

  /**
   * A pre-defined cursor.
   */
  keyword: CursorKeyword;
}
/**
 * A [cursor image](https://www.w3.org/TR/2021/WD-css-ui-4-20210316/#cursor) value, used in the `cursor` property.
 *
 * See [Cursor](Cursor).
 */
export interface CursorImage {
  /**
   * The location in the image where the mouse pointer appears.
   * @minItems 2
   * @maxItems 2
   */
  hotspot?: [number, number] | null;

  /**
   * A url to the cursor image.
   */
  url: Url;
}
/**
 * A value for the [caret](https://www.w3.org/TR/2021/WD-css-ui-4-20210316/#caret) shorthand property.
 */
export interface Caret {
  /**
   * The caret color.
   */
  color: ColorOrAuto;

  /**
   * The caret shape.
   */
  shape: CaretShape;
}
/**
 * A value for the [list-style](https://www.w3.org/TR/2020/WD-css-lists-3-20201117/#list-style-property) shorthand property.
 */
export interface ListStyle {
  /**
   * The list marker image.
   */
  image: Image;

  /**
   * The list style type.
   */
  listStyleType: ListStyleType;

  /**
   * The position of the list marker.
   */
  position: ListStylePosition;
}
/**
 * A value for the [composes](https://github.com/css-modules/css-modules/#dependencies) property from CSS modules.
 */
export interface Composes {
  /**
   * Where the class names are composed from.
   */
  from?: Specifier | null;

  /**
   * The source location of the `composes` property.
   */
  loc: Location;

  /**
   * A list of class names to compose.
   */
  names: String[];
}
/**
 * An [`inset()`](https://www.w3.org/TR/css-shapes-1/#funcdef-inset) rectangle shape.
 */
export interface InsetRect {
  /**
   * A corner radius for the rectangle.
   */
  radius: BorderRadius;

  /**
   * The rectangle.
   */
  rect: RectFor_DimensionPercentageFor_LengthValue;
}
/**
 * A [`circle()`](https://www.w3.org/TR/css-shapes-1/#funcdef-circle) shape.
 */
export interface Circle2 {
  /**
   * The position of the center of the circle.
   */
  position: Position;

  /**
   * The radius of the circle.
   */
  radius: ShapeRadius;
}
/**
 * An [`ellipse()`](https://www.w3.org/TR/css-shapes-1/#funcdef-ellipse) shape.
 */
export interface Ellipse2 {
  /**
   * The position of the center of the ellipse.
   */
  position: Position;

  /**
   * The x-radius of the ellipse.
   */
  radiusX: ShapeRadius;

  /**
   * The y-radius of the ellipse.
   */
  radiusY: ShapeRadius;
}
/**
 * A [`polygon()`](https://www.w3.org/TR/css-shapes-1/#funcdef-polygon) shape.
 */
export interface Polygon {
  /**
   * The fill rule used to determine the interior of the polygon.
   */
  fillRule: FillRule;

  /**
   * The points of each vertex of the polygon.
   */
  points: Point[];
}
/**
 * A point within a `polygon()` shape.
 *
 * See [Polygon](Polygon).
 */
export interface Point {
  /**
   * The x position of the point.
   */
  x: DimensionPercentageFor_LengthValue;

  /**
   * the y position of the point.
   */
  y: DimensionPercentageFor_LengthValue;
}
/**
 * A value for the [mask](https://www.w3.org/TR/css-masking-1/#the-mask) shorthand property.
 */
export interface Mask {
  /**
   * The box in which the mask is clipped.
   */
  clip: MaskClip;

  /**
   * How the mask is composited with the element.
   */
  composite: MaskComposite;

  /**
   * The mask image.
   */
  image: Image;

  /**
   * How the mask image is interpreted.
   */
  mode: MaskMode;

  /**
   * The origin of the mask.
   */
  origin: GeometryBox;

  /**
   * The position of the mask.
   */
  position: Position;

  /**
   * How the mask repeats.
   */
  repeat: BackgroundRepeat;

  /**
   * The size of the mask image.
   */
  size: BackgroundSize;
}
/**
 * A value for the [mask-border](https://www.w3.org/TR/css-masking-1/#the-mask-border) shorthand property.
 */
export interface MaskBorder {
  /**
   * How the mask image is interpreted.
   */
  mode: MaskBorderMode;

  /**
   * The amount that the image extends beyond the border box.
   */
  outset: RectFor_LengthOrNumber;

  /**
   * How the mask image is scaled and tiled.
   */
  repeat: BorderImageRepeat;

  /**
   * The offsets that define where the image is sliced.
   */
  slice: BorderImageSlice;

  /**
   * The mask image.
   */
  source: Image;

  /**
   * The width of the mask image.
   */
  width: RectFor_BorderImageSideWidth;
}
/**
 * A [`drop-shadow()`](https://drafts.fxtf.org/filter-effects-1/#funcdef-filter-drop-shadow) filter function.
 */
export interface DropShadow {
  /**
   * The blur radius of the drop shadow.
   */
  blur: Length;

  /**
   * The color of the drop shadow.
   */
  color: CssColor;

  /**
   * The x offset of the drop shadow.
   */
  xOffset: Length;

  /**
   * The y offset of the drop shadow.
   */
  yOffset: Length;
}
/**
 * A value for the [container](https://drafts.csswg.org/css-contain-3/#container-shorthand) shorthand property.
 */
export interface Container {
  /**
   * The container type.
   */
  containerType: ContainerType;

  /**
   * The container name.
   */
  name: ContainerNameList;
}
export interface ColorScheme {
  dark: boolean;
  light: boolean;
  only: boolean;
}
/**
 * A known property with an unparsed value.
 *
 * This type is used when the value of a known property could not be parsed, e.g. in the case css `var()` references are encountered. In this case, the raw tokens are stored instead.
 */
export interface UnparsedProperty {
  /**
   * The id of the property.
   */
  propertyId: PropertyId;

  /**
   * The property value, stored as a raw token list.
   */
  value: TokenOrValue[];
}
/**
 * A CSS custom property, representing any unknown property.
 */
export interface CustomProperty {
  /**
   * The name of the property.
   */
  name: CustomPropertyName;

  /**
   * The property value, stored as a raw token list.
   */
  value: TokenOrValue[];
}
export interface AttrOperation {
  caseSensitivity?: {| ...ParsedCaseSensitivity, ...string |};
  operator: AttrSelectorOperator;
  value: string;
}
/**
 * A [view transition part selector](https://w3c.github.io/csswg-drafts/css-view-transitions-1/#typedef-pt-name-selector).
 */
export interface ViewTransitionPartSelector {
  /**
   * A list of view transition classes.
   */
  classes: String[];

  /**
   * The view transition part name.
   */
  name?: ViewTransitionPartName | null;
}
/**
 * A [@keyframes](https://drafts.csswg.org/css-animations/#keyframes) rule.
 */
export interface KeyframesRule<D = Declaration> {
  /**
   * A list of keyframes in the animation.
   */
  keyframes: Keyframe<D>[];

  /**
   * The location of the rule in the source file.
   */
  loc: Location2;

  /**
   * The animation name. <keyframes-name> = <custom-ident> | <string>
   */
  name: KeyframesName;

  /**
   * A vendor prefix for the rule, e.g. `@-webkit-keyframes`.
   */
  vendorPrefix: VendorPrefix;
}
/**
 * An individual keyframe within an `@keyframes` rule.
 *
 * See [KeyframesRule](KeyframesRule).
 */
export interface Keyframe<D = Declaration> {
  /**
   * The declarations for this keyframe.
   */
  declarations: DeclarationBlock<D>;

  /**
   * A list of keyframe selectors to associate with the declarations in this keyframe.
   */
  selectors: KeyframeSelector[];
}
/**
 * A percentage of a given timeline range
 */
export interface TimelineRangePercentage {
  /**
   * The name of the timeline range.
   */
  name: TimelineRangeName;

  /**
   * The percentage progress between the start and end of the range.
   */
  percentage: number;
}
/**
 * A [@font-face](https://drafts.csswg.org/css-fonts/#font-face-rule) rule.
 */
export interface FontFaceRule {
  /**
   * The location of the rule in the source file.
   */
  loc: Location2;

  /**
   * Declarations in the `@font-face` rule.
   */
  properties: FontFaceProperty[];
}
/**
 * A `url()` value for the [src](https://drafts.csswg.org/css-fonts/#src-desc) property in an `@font-face` rule.
 */
export interface UrlSource {
  /**
   * Optional `format()` function.
   */
  format?: FontFormat | null;

  /**
   * Optional `tech()` function.
   */
  tech: FontTechnology[];

  /**
   * The URL.
   */
  url: Url;
}
/**
 * A contiguous range of Unicode code points.
 *
 * Cannot be empty. Can represent a single code point when start == end.
 */
export interface UnicodeRange {
  /**
   * Inclusive end of the range. In [0, 0x10FFFF].
   */
  end: number;

  /**
   * Inclusive start of the range. In [0, end].
   */
  start: number;
}
/**
 * A [@font-palette-values](https://drafts.csswg.org/css-fonts-4/#font-palette-values) rule.
 */
export interface FontPaletteValuesRule {
  /**
   * The location of the rule in the source file.
   */
  loc: Location2;

  /**
   * The name of the font palette.
   */
  name: String;

  /**
   * Declarations in the `@font-palette-values` rule.
   */
  properties: FontPaletteValuesProperty[];
}
/**
 * A value for the [override-colors](https://drafts.csswg.org/css-fonts-4/#override-color) property in an `@font-palette-values` rule.
 */
export interface OverrideColors {
  /**
   * The replacement color.
   */
  color: CssColor;

  /**
   * The index of the color within the palette to override.
   */
  index: number;
}
/**
 * A [@font-feature-values](https://drafts.csswg.org/css-fonts/#font-feature-values) rule.
 */
export interface FontFeatureValuesRule {
  /**
   * The location of the rule in the source file.
   */
  loc: Location2;

  /**
   * The name of the font feature values.
   */
  name: String[];

  /**
   * The rules within the `@font-feature-values` rule.
   */
  rules: {
    [k: string]: FontFeatureSubrule,
  };
}
/**
 * A sub-rule of `@font-feature-values` https://drafts.csswg.org/css-fonts/#font-feature-values-syntax
 */
export interface FontFeatureSubrule {
  /**
   * The declarations within the `@font-feature-values` sub-rules.
   */
  declarations: {
    [k: string]: number[],
  };

  /**
   * The location of the rule in the source file.
   */
  loc: Location2;

  /**
   * The name of the `@font-feature-values` sub-rule.
   */
  name: FontFeatureSubruleType;
}
/**
 * A [@page](https://www.w3.org/TR/css-page-3/#at-page-rule) rule.
 */
export interface PageRule<D = Declaration> {
  /**
   * The declarations within the `@page` rule.
   */
  declarations: DeclarationBlock<D>;

  /**
   * The location of the rule in the source file.
   */
  loc: Location2;

  /**
   * The nested margin rules.
   */
  rules: PageMarginRule<D>[];

  /**
   * A list of page selectors.
   */
  selectors: PageSelector[];
}
/**
 * A [page margin rule](https://www.w3.org/TR/css-page-3/#margin-at-rules) rule.
 */
export interface PageMarginRule<D = Declaration> {
  /**
   * The declarations within the rule.
   */
  declarations: DeclarationBlock<D>;

  /**
   * The location of the rule in the source file.
   */
  loc: Location2;

  /**
   * The margin box identifier for this rule.
   */
  marginBox: PageMarginBox;
}
/**
 * A [page selector](https://www.w3.org/TR/css-page-3/#typedef-page-selector) within a `@page` rule.
 *
 * Either a name or at least one pseudo class is required.
 */
export interface PageSelector {
  /**
   * An optional named page type.
   */
  name?: String | null;

  /**
   * A list of page pseudo classes.
   */
  pseudoClasses: PagePseudoClass[];
}
/**
 * A [@supports](https://drafts.csswg.org/css-conditional-3/#at-supports) rule.
 */
export interface SupportsRule<D = Declaration, M = MediaQuery> {
  /**
   * The supports condition.
   */
  condition: SupportsCondition;

  /**
   * The location of the rule in the source file.
   */
  loc: Location2;

  /**
   * The rules within the `@supports` rule.
   */
  rules: Rule<D, M>[];
}
/**
 * A [@counter-style](https://drafts.csswg.org/css-counter-styles/#the-counter-style-rule) rule.
 */
export interface CounterStyleRule<D = Declaration> {
  /**
   * Declarations in the `@counter-style` rule.
   */
  declarations: DeclarationBlock<D>;

  /**
   * The location of the rule in the source file.
   */
  loc: Location2;

  /**
   * The name of the counter style to declare.
   */
  name: String;
}
/**
 * A [@namespace](https://drafts.csswg.org/css-namespaces/#declaration) rule.
 */
export interface NamespaceRule {
  /**
   * The location of the rule in the source file.
   */
  loc: Location2;

  /**
   * An optional namespace prefix to declare, or `None` to declare the default namespace.
   */
  prefix?: String | null;

  /**
   * The url of the namespace.
   */
  url: String;
}
/**
 * A [@-moz-document](https://www.w3.org/TR/2012/WD-css3-conditional-20120911/#at-document) rule.
 *
 * Note that only the `url-prefix()` function with no arguments is supported, and only the `-moz` prefix is allowed since Firefox was the only browser that ever implemented this rule.
 */
export interface MozDocumentRule<D = Declaration, M = MediaQuery> {
  /**
   * The location of the rule in the source file.
   */
  loc: Location2;

  /**
   * Nested rules within the `@-moz-document` rule.
   */
  rules: Rule<D, M>[];
}
/**
 * A [@nest](https://www.w3.org/TR/css-nesting-1/#at-nest) rule.
 */
export interface NestingRule<D = Declaration, M = MediaQuery> {
  /**
   * The location of the rule in the source file.
   */
  loc: Location2;

  /**
   * The style rule that defines the selector and declarations for the `@nest` rule.
   */
  style: StyleRule<D, M>;
}
/**
 * A [nested declarations](https://drafts.csswg.org/css-nesting/#nested-declarations-rule) rule.
 */
export interface NestedDeclarationsRule<D = Declaration> {
  /**
   * The style rule that defines the selector and declarations for the `@nest` rule.
   */
  declarations: DeclarationBlock<D>;

  /**
   * The location of the rule in the source file.
   */
  loc: Location2;
}
/**
 * A [@viewport](https://drafts.csswg.org/css-device-adapt/#atviewport-rule) rule.
 */
export interface ViewportRule<D = Declaration> {
  /**
   * The declarations within the `@viewport` rule.
   */
  declarations: DeclarationBlock<D>;

  /**
   * The location of the rule in the source file.
   */
  loc: Location2;

  /**
   * The vendor prefix for this rule, e.g. `@-ms-viewport`.
   */
  vendorPrefix: VendorPrefix;
}
/**
 * A [@custom-media](https://drafts.csswg.org/mediaqueries-5/#custom-mq) rule.
 */
export interface CustomMediaRule<M = MediaQuery> {
  /**
   * The location of the rule in the source file.
   */
  loc: Location2;

  /**
   * The name of the declared media query.
   */
  name: String;

  /**
   * The media query to declare.
   */
  query: MediaList<M>;
}
/**
 * A [@layer statement](https://drafts.csswg.org/css-cascade-5/#layer-empty) rule.
 *
 * See also [LayerBlockRule](LayerBlockRule).
 */
export interface LayerStatementRule {
  /**
   * The location of the rule in the source file.
   */
  loc: Location2;

  /**
   * The layer names to declare.
   */
  names: String[][];
}
/**
 * A [@layer block](https://drafts.csswg.org/css-cascade-5/#layer-block) rule.
 */
export interface LayerBlockRule<D = Declaration, M = MediaQuery> {
  /**
   * The location of the rule in the source file.
   */
  loc: Location2;

  /**
   * The name of the layer to declare, or `None` to declare an anonymous layer.
   */
  name?: String[] | null;

  /**
   * The rules within the `@layer` rule.
   */
  rules: Rule<D, M>[];
}
/**
 * A [@property](https://drafts.css-houdini.org/css-properties-values-api/#at-property-rule) rule.
 */
export interface PropertyRule {
  /**
   * Whether the custom property is inherited.
   */
  inherits: boolean;

  /**
   * An optional initial value for the custom property.
   */
  initialValue?: ParsedComponent | null;

  /**
   * The location of the rule in the source file.
   */
  loc: Location2;

  /**
   * The name of the custom property to declare.
   */
  name: String;

  /**
   * A syntax string to specify the grammar for the custom property.
   */
  syntax: SyntaxString;
}
/**
 * A [syntax component](https://drafts.css-houdini.org/css-properties-values-api/#syntax-component) within a [SyntaxString](SyntaxString).
 *
 * A syntax component consists of a component kind an a multiplier, which indicates how the component may repeat during parsing.
 */
export interface SyntaxComponent {
  /**
   * The kind of component.
   */
  kind: SyntaxComponentKind;

  /**
   * A multiplier for the component.
   */
  multiplier: Multiplier;
}
/**
 * A [@container](https://drafts.csswg.org/css-contain-3/#container-rule) rule.
 */
export interface ContainerRule<D = Declaration, M = MediaQuery> {
  /**
   * The container condition.
   */
  condition: ContainerCondition<D>;

  /**
   * The location of the rule in the source file.
   */
  loc: Location2;

  /**
   * The name of the container.
   */
  name?: String | null;

  /**
   * The rules within the `@container` rule.
   */
  rules: Rule<D, M>[];
}
/**
 * A [@scope](https://drafts.csswg.org/css-cascade-6/#scope-atrule) rule.
 * @scope (<scope-start>) [to (<scope-end>)]? { <stylesheet> }
 */
export interface ScopeRule<D = Declaration, M = MediaQuery> {
  /**
   * The location of the rule in the source file.
   */
  loc: Location2;

  /**
   * Nested rules within the `@scope` rule.
   */
  rules: Rule<D, M>[];

  /**
   * A selector list used to identify any scoping limits.
   */
  scopeEnd?: SelectorList | null;

  /**
   * A selector list used to identify the scoping root(s).
   */
  scopeStart?: SelectorList | null;
}
/**
 * A [@starting-style](https://drafts.csswg.org/css-transitions-2/#defining-before-change-style-the-starting-style-rule) rule.
 */
export interface StartingStyleRule<D = Declaration, M = MediaQuery> {
  /**
   * The location of the rule in the source file.
   */
  loc: Location2;

  /**
   * Nested rules within the `@starting-style` rule.
   */
  rules: Rule<D, M>[];
}
/**
 * A [@view-transition](https://drafts.csswg.org/css-view-transitions-2/#view-transition-rule) rule.
 */
export interface ViewTransitionRule {
  /**
   * The location of the rule in the source file.
   */
  loc: Location2;

  /**
   * Declarations in the `@view-transition` rule.
   */
  properties: ViewTransitionProperty[];
}
/**
 * An unknown at-rule, stored as raw tokens.
 */
export interface UnknownAtRule {
  /**
   * The contents of the block, if any.
   */
  block?: TokenOrValue[] | null;

  /**
   * The location of the rule in the source file.
   */
  loc: Location2;

  /**
   * The name of the at-rule (without the @).
   */
  name: String;

  /**
   * The prelude of the rule.
   */
  prelude: TokenOrValue[];
}
