# API Keys - Required for data collection
# Google Places API Key (required for Google Places data source)
VITE_GOOGLE_PLACES_API_KEY=your_google_places_api_key_here

# OpenAI API Key (required for Yellow Pages AI-powered scraping)
# Get your API key from: https://platform.openai.com/api-keys
VITE_OPENAI_API_KEY=your_openai_api_key_here

# Groq API Key (alternative to OpenAI for Yellow Pages LLM extraction)
VITE_GROQ_API_KEY=your_groq_api_key_here

# Application Configuration
# Default search location (Pittsburgh coordinates)
VITE_DEFAULT_LAT=40.440624
VITE_DEFAULT_LNG=-79.995888

# Default search radius in meters (10 miles = 16093 meters)
VITE_DEFAULT_RADIUS=16093

# Rate limiting settings (requests per second)
VITE_GOOGLE_PLACES_DELAY=1000
VITE_YELLOW_PAGES_DELAY=500
VITE_OVERPASS_DELAY=500

# Development settings
VITE_LOG_LEVEL=info
VITE_ENABLE_DEBUG=false
